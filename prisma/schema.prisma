generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "debian-openssl-3.0.x"]
  // previewFeatures = ["omitApi"]
}

generator zod {
  provider = "zod-prisma-types"
  output   = "../schemas/prisma_zod"
}

datasource db {
  provider = "mysql"
  url      = "mysql://root:4450qqwv@localhost:3306/warehouse"
}

// 用户表
model User {
  id                    Int                     @id @default(autoincrement()) @db.Int //用户id
  code                  String                  @default("0") @db.VarChar(255) //用户编码
  name                  String                  @db.VarChar(255) //用户名称
  username              String                  @db.VarChar(255) //用户登录名
  password              String                  @db.VarChar(255) //用户密码
  role_id               Int                     @db.Int //用户角色id
  role                  Role                    @relation(fields: [role_id], references: [id]) //用户角色
  email                 String                  @default("") @db.VarChar(255) //用户邮箱
  contactTel            String                  @default("") @db.VarChar(255) //用户电话
  createAt              DateTime                @default(now()) //创建时间
  updateAt              DateTime                @updatedAt //更新时间
  status                UserStatus              @default(active) //用户状态: active-正常, inactive-锁定
  WarehouseOnUser       WarehouseOnUser[] //用户仓库
  Supplier              Supplier[] //用户添加供应商
  Materiel              Materiel[]
  ProductionTaskUsers   ProductionTaskUsers[]
  PurchaseOrders        PurchaseOrder[] //用户创建的采购订单
  PurchaseReceipts      PurchaseReceiptRecord[] //用户创建的收货记录
  faceFeature           UserFaceFeature?
  ProductionReport      ProductionReport[]
  Customer              Customer[]
  CustomerReceivingInfo CustomerReceivingInfo[]
  SalesOutRecord        SalesOutRecord[] //用户创建的销售出库记录
  StockCheckUser        StockCheck[]            @relation("StockCheckUser") //用户创建的盘点单
  StockCheckApprover    StockCheck[]            @relation("StockCheckApprover") //用户审核的盘点单
  StockCheckItem        StockCheckItem[] //用户盘点的明细项
  ProductionTaskCreated ProductionTask[]        @relation("ProductionTaskCreator") //用户创建的生产工单
}

enum UserStatus {
  active //正常
  inactive //锁定
}

// enum Role {
//   SuperAdmin
//   SellerAdmin
//   WarehouseAdmin
//   PurchaseAdmin
//   FinanceAdmin
//   ProductionAdmin
//   ProductionUser
//   Tourist
// }

// 用户角色
model Role {
  id         Int        @id @default(autoincrement()) //角色id
  code       String     @unique @db.VarChar(100) //角色编码
  name       String     @db.VarChar(255) //角色名称
  User       User[]
  Permission Json       @db.Json
  Menu       Json       @db.Json
  RoleMenu   RoleMenu[]
}

enum MenuType {
  item
  group
  sub
}

model Menu {
  id       Int        @id @default(autoincrement())
  name     String
  title    String
  icon     String?
  sort     Int        @default(999)
  disabled Boolean    @default(false)
  type     MenuType
  children Menu[]     @relation("MenuToMenu")
  parentId Int?
  parent   Menu?      @relation("MenuToMenu", fields: [parentId], references: [id])
  RoleMenu RoleMenu[]
}

model RoleMenu {
  role_id Int  @db.Int
  role    Role @relation(fields: [role_id], references: [id])
  menu_id Int  @db.Int
  menu    Menu @relation(fields: [menu_id], references: [id])

  @@unique([role_id, menu_id])
}

// model Menu {
//   id        Int      @id @default(autoincrement())
//   name      String
//   path      String
//   icon      String?
//   sort      Int      @default(0)
//   parentId  Int?
//   parent    Menu?    @relation("MenuToMenu", fields: [parentId], references: [id])
//   children  Menu[]   @relation("MenuToMenu")
//   component String?
//   hidden    Boolean  @default(false)
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

model Permission {
  id   Int    @id @default(autoincrement()) //权限id
  code String @unique @db.VarChar(100) //权限编码
  name String @db.VarChar(255) //权限名称
}

// 仓库
model Warehouse {
  id              Int               @id @default(autoincrement()) @db.Int
  name            String            @db.VarChar(255) //仓库名称
  address         String?           @db.Text //仓库地址
  WarehouseOnUser WarehouseOnUser[] //仓库关联用户
  lock            Boolean           @default(false) @db.TinyInt //仓库锁定
  PurchaseOrder   PurchaseOrder[]
  Stock           Stock[] //仓库库存记录
  SalesOutRecord  SalesOutRecord[] //销售出库记录
  StockCheck      StockCheck[] //仓库盘点记录
  type            WarehouseType     @default(production) //仓库类型
}

//仓库类型
enum WarehouseType {
  waste //废料库
  sales //销售库
  production //生产库
  finished //成品库
}

// 仓库关联用户
model WarehouseOnUser {
  id           Int       @id @default(autoincrement()) @db.Int //仓库关联用户id
  user_id      Int //用户id
  user         User      @relation(fields: [user_id], references: [id]) //用户
  warehouse_id Int //仓库id
  warehouse    Warehouse @relation(fields: [warehouse_id], references: [id]) //仓库

  @@map("warehouse_user")
}

// 供应商
model Supplier {
  id            Int             @id @default(autoincrement()) @db.Int //供应商id
  name          String          @db.VarChar(255) //供应商名称
  citycode      Json            @db.Json //供应商城市编码
  address       String          @db.Text //供应商地址
  contactName   String          @db.VarChar(255) //联系人
  contactPhone  String          @db.VarChar(255) //联系人电话
  email         String          @db.VarChar(255) //邮箱
  createAt      DateTime        @default(now()) //创建时间
  updateAt      DateTime        @updatedAt //更新时间
  lock          Boolean         @default(false) @db.TinyInt //供应商锁定
  user_id       Int             @db.Int //用户id
  Admin         User            @relation(fields: [user_id], references: [id]) //用户
  PurchaseOrder PurchaseOrder[] //供应商相关的采购订单
}

// 系统安装
model System {
  id              Int      @id @default(autoincrement()) @db.Int //系统安装id
  installed       Boolean  @default(false) @db.TinyInt //是否安装
  createAt        DateTime @default(now()) //创建时间
  menuCount       Int      @default(0) @db.Int //菜单数量
  permissionCount Int      @default(0) @db.Int //权限数量
}

// 常见缺陷表
model CommonDefect {
  id          Int      @id @default(autoincrement()) @db.Int //缺陷id
  name        String   @db.VarChar(255) //缺陷名称
  code        String   @db.VarChar(255) //缺陷编码
  description String   @db.Text //缺陷描述
  createAt    DateTime @default(now()) //创建时间
  updateAt    DateTime @updatedAt //更新时间
}

//物料表
model Materiel {
  id                    Int                     @id @default(autoincrement()) @db.Int //物料id
  category              String                  @db.VarChar(255) //品类分类
  attribute             String                  @db.VarChar(255) //属性分类
  type                  String                  @db.VarChar(255) //物料类型
  name                  String                  @db.VarChar(255) //物料名称
  code                  String                  @unique @db.VarChar(255) //物料编码
  description           String?                 @db.Text //物料描述
  model                 String                  @db.VarChar(255) //型号
  unit                  String                  @db.VarChar(255) //单位
  specification         String?                 @db.VarChar(255) //规格
  picpath               Json?                   @db.Json //图片
  stock                 Decimal                 @default(0) @db.Decimal(20, 4) //总库存
  onway_stock           Decimal                 @default(0) @db.Decimal(20, 4) //在途数量(在途数量=采购在途+生产在途-领料在途)
  lowstock              Decimal                 @default(0) @db.Decimal(20, 4) //低库存
  maxstock              Decimal                 @default(0) @db.Decimal(20, 4) //最大库存
  createuser_id         Int                     @db.Int //创建用户id
  useable               Boolean                 @default(true) @db.TinyInt
  Admin                 User                    @relation(fields: [createuser_id], references: [id])
  createAt              DateTime                @default(now()) //创建时间
  updateAt              DateTime                @updatedAt //更新时间
  ProductionTask        ProductionTask[]
  ProductionPlanItem    ProductionPlanItem[]
  PurchaseOrderItem     PurchaseOrderItem[] //采购订单物料项
  Stock                 Stock[] //物料库存记录
  // 物料的BOM关系
  bomParents            MaterielBom[]           @relation("BomParent")
  bomChilds             MaterielBom[]           @relation("BomChild")
  ProductionOutRecord   ProductionOutRecord[]
  subBomParents         MaterielSubBom[]        @relation("SubMaterielParent")
  subBomChilds          MaterielSubBom[]        @relation("SubMaterielChild")
  ProductionInRecord    ProductionInRecord[]
  WasteProcessingRecord WasteProcessingRecord[]
  DisinfectionRecord    DisinfectionRecord[]
  SalesOutRecord        SalesOutRecord[]
  StockCheckItem        StockCheckItem[]
}

//物料BOM表
model MaterielBom {
  id       Int      @id @default(autoincrement())
  parentId Int // 父物料ID
  childId  Int // 子物料ID
  quantity Decimal  @db.Decimal(20, 4) // 用量
  remark   String? // 备注
  createAt DateTime @default(now())
  updateAt DateTime @updatedAt

  // 关联
  parent Materiel @relation("BomParent", fields: [parentId], references: [id])
  child  Materiel @relation("BomChild", fields: [childId], references: [id])

  @@index([parentId])
  @@index([childId])
}

//副产物
model MaterielSubBom {
  id       Int      @id @default(autoincrement()) @db.Int //副产物id
  parentId Int      @db.Int //父物料id
  parent   Materiel @relation("SubMaterielParent", fields: [parentId], references: [id]) //父物料
  childId  Int      @db.Int //子物料id
  child    Materiel @relation("SubMaterielChild", fields: [childId], references: [id]) //子物料
  quantity Decimal  @db.Decimal(20, 4) //数量
  createAt DateTime @default(now()) //创建时间
  updateAt DateTime @updatedAt //更新时间

  @@index([parentId])
  @@index([childId])
}

//生产工单
model ProductionTask {
  id                   Int                   @id @default(autoincrement()) @db.Int //生产工单id
  code                 String                @db.VarChar(255) //生产工单编码
  description          String?               @db.Text //生产工单描述
  materiel_id          Int                   @db.Int //物料id
  Materiel             Materiel              @relation(fields: [materiel_id], references: [id]) //物料
  quantity             Decimal               @db.Decimal(20, 4) //计划数量
  completed_quantity   Decimal               @default(0) @db.Decimal(20, 4) //完成数量
  qualified_quantity   Decimal               @default(0) @db.Decimal(20, 4) //合格品数量
  unqualified_quantity Decimal               @default(0) @db.Decimal(20, 4) //不合格品数量
  createuser_id        Int                   @db.Int //创建用户id
  createUser           User                  @relation("ProductionTaskCreator", fields: [createuser_id], references: [id]) //创建用户
  createAt             DateTime              @default(now()) //创建时间
  updateAt             DateTime              @updatedAt //更新时间
  startAt              DateTime //计划开始时间
  endAt                DateTime //计划结束时间
  actualStartAt        DateTime? //实际开始时间
  actualEndAt          DateTime? //实际结束时间
  status               Int                   @default(0) @db.TinyInt //生产工单状态: 0-草稿, 1-待生产, 2-生产中, 3-已完成, 4-已取消
  finishCondition      String                @default("quantity") @db.VarChar(50) //完成条件: quantity-达到数量, time-到达时间
  users                ProductionTaskUsers[] //生产工单用户关联
  ProductionOutRecord  ProductionOutRecord[]
  ProductionReport     ProductionReport[]
  ProductionInRecord   ProductionInRecord[]
}

//生产工单用户关联表
model ProductionTaskUsers {
  id              Int            @id @default(autoincrement()) @db.Int
  production_task ProductionTask @relation(fields: [task_id], references: [id])
  task_id         Int            @db.Int
  user            User           @relation(fields: [user_id], references: [id])
  user_id         Int            @db.Int

  @@unique([task_id, user_id])
}

//生产报工记录
model ProductionReport {
  id                   Int            @id @default(autoincrement()) @db.Int //生产报工记录id
  production_task_id   Int            @db.Int //生产工单id
  production_task      ProductionTask @relation(fields: [production_task_id], references: [id]) //生产工单
  user_id              Int            @db.Int //用户id
  user                 User           @relation(fields: [user_id], references: [id]) //用户
  quantity             Decimal        @db.Decimal(20, 4) //合格品数量
  unqualified_quantity Decimal        @default(0) @db.Decimal(20, 4) //不合格品数量
  note                 String?        @db.Text //备注
  createAt             DateTime       @default(now()) //创建时间
  updateAt             DateTime       @updatedAt //更新时间
}

//生产计划
model ProductionPlan {
  id                 Int                  @id @default(autoincrement()) @db.Int //生产计划id
  code               String               @db.VarChar(255) //生产计划编码
  name               String               @db.VarChar(255) //生产计划名称
  description        String?              @db.Text //生产计划描述
  startTime          DateTime //计划开始时间
  endTime            DateTime //计划结束时间
  status             Int                  @default(0) @db.TinyInt //生产计划状态
  completion         Float                @default(0) //生产计划完成度
  createAt           DateTime             @default(now()) //创建时间
  updateAt           DateTime             @updatedAt //更新时间
  ProductionPlanItem ProductionPlanItem[]
}

//生产计划明细
model ProductionPlanItem {
  id                 Int            @id @default(autoincrement()) @db.Int //生产计划明细id
  production_plan    ProductionPlan @relation(fields: [production_plan_id], references: [id])
  production_plan_id Int            @db.Int //生产计划id
  materiel_id        Int            @db.Int //物料id
  Materiel           Materiel       @relation(fields: [materiel_id], references: [id]) //物料
  quantity           Decimal        @db.Decimal(20, 4) //数量
  createAt           DateTime       @default(now()) //创建时间
  updateAt           DateTime       @updatedAt //更新时间
}

// 采购订单
model PurchaseOrder {
  id                   Int                     @id @default(autoincrement()) @db.Int //采购订单id
  orderNo              String                  @db.VarChar(255) //订单编号
  supplierId           Int                     @db.Int //供应商id
  supplier             Supplier                @relation(fields: [supplierId], references: [id]) //供应商
  totalAmount          Decimal                 @db.Decimal(20, 4) //订单总金额
  expectedDeliveryDate DateTime? //预计到货日期
  status               String                  @default("draft") @db.VarChar(50) //订单状态: draft-草稿, pending-待审核, approved-已审核, rejected-已拒绝, partially_received-部分收货, completed-完成, cancelled-取消
  note                 String?                 @db.Text //订单备注
  userId               Int                     @db.Int //创建用户id
  user                 User                    @relation(fields: [userId], references: [id]) //创建用户
  submittedAt          DateTime? //提交审核时间
  approvedBy           Int?                    @db.Int //审批人id
  approvedAt           DateTime? //审批时间
  rejectReason         String?                 @db.Text //拒绝原因
  createdAt            DateTime                @default(now()) //创建时间
  updatedAt            DateTime                @updatedAt //更新时间
  warehouseId          Int?                    @db.Int //仓库id
  warehouse            Warehouse?              @relation(fields: [warehouseId], references: [id]) //仓库
  items                PurchaseOrderItem[] //订单物料项
  receiptRecords       PurchaseReceiptRecord[] //收货记录
}

// 采购订单物料项
model PurchaseOrderItem {
  id              Int                   @id @default(autoincrement()) @db.Int //订单物料项id
  purchaseOrderId Int                   @db.Int //采购订单id
  purchaseOrder   PurchaseOrder         @relation(fields: [purchaseOrderId], references: [id]) //采购订单
  materialId      Int                   @db.Int //物料id
  material        Materiel              @relation(fields: [materialId], references: [id]) //物料
  quantity        Decimal               @db.Decimal(20, 4) //数量
  unitPrice       Decimal               @db.Decimal(20, 4) //单价
  note            String?               @db.Text //备注
  createdAt       DateTime              @default(now()) //创建时间
  updatedAt       DateTime              @updatedAt //更新时间
  receiptItems    PurchaseReceiptItem[] //收货项
}

// 采购收货记录
model PurchaseReceiptRecord {
  id              Int                   @id @default(autoincrement()) @db.Int //收货记录id
  purchaseOrderId Int                   @db.Int //采购订单id
  purchaseOrder   PurchaseOrder         @relation(fields: [purchaseOrderId], references: [id]) //采购订单
  userId          Int                   @db.Int //收货人id
  user            User                  @relation(fields: [userId], references: [id]) //收货人
  note            String?               @db.Text //备注
  createdAt       DateTime              @default(now()) //创建时间
  updatedAt       DateTime              @updatedAt //更新时间
  items           PurchaseReceiptItem[] //收货物料项
}

// 采购收货物料项
model PurchaseReceiptItem {
  id                Int                   @id @default(autoincrement()) @db.Int //收货物料项id
  purchaseReceiptId Int                   @db.Int //收货记录id
  purchaseReceipt   PurchaseReceiptRecord @relation(fields: [purchaseReceiptId], references: [id]) //收货记录
  orderItemId       Int                   @db.Int //订单物料项id
  orderItem         PurchaseOrderItem     @relation(fields: [orderItemId], references: [id]) //订单物料项
  receivedQuantity  Decimal               @db.Decimal(20, 4) //收货数量
  batchNo           String                @db.VarChar(255) //批号
  note              String?               @db.Text //备注
  createdAt         DateTime              @default(now()) //创建时间
  updatedAt         DateTime              @updatedAt //更新时间
}

// 库存表
model Stock {
  id              Int       @id @default(autoincrement()) @db.Int //库存id
  materiel_id     Int       @db.Int //物料id
  materiel        Materiel  @relation(fields: [materiel_id], references: [id]) //物料
  warehouse_id    Int       @db.Int //仓库id
  warehouse       Warehouse @relation(fields: [warehouse_id], references: [id]) //仓库
  batch_no        String    @db.VarChar(255) //批号
  quantity        Decimal   @db.Decimal(20, 4) //数量
  production_date DateTime? //生产日期
  expiry_date     DateTime? //有效期
  location        String?   @db.VarChar(255) //库位
  status          String    @default("normal") @db.VarChar(50) //状态：normal-正常, locked-锁定, expired-过期
  note            String?   @db.Text //备注
  createdAt       DateTime  @default(now()) //创建时间
  updatedAt       DateTime  @updatedAt //更新时间

  @@unique([materiel_id, warehouse_id, batch_no])
}

// 生产领料记录
model ProductionOutRecord {
  id               Int            @id @default(autoincrement()) @db.Int //生产领料记录id
  productionTaskId Int            @db.Int //生产工单id
  productionTask   ProductionTask @relation(fields: [productionTaskId], references: [id]) //生产工单
  materiel_id      Int            @db.Int //物料id
  materiel         Materiel       @relation(fields: [materiel_id], references: [id]) //物料
  quantity         Decimal        @db.Decimal(20, 4) //领料数量
  batch_no         String         @db.VarChar(255) //批号
  note             String?        @db.Text //备注
  createdAt        DateTime       @default(now()) //创建时间
  updatedAt        DateTime       @updatedAt //更新时间
}

// 生产入库记录
model ProductionInRecord {
  id               Int            @id @default(autoincrement()) @db.Int //生产入库记录id
  productionTaskId Int            @db.Int //生产工单id
  productionTask   ProductionTask @relation(fields: [productionTaskId], references: [id]) //生产工单
  materiel_id      Int            @db.Int //物料id
  materiel         Materiel       @relation(fields: [materiel_id], references: [id]) //物料
  quantity         Decimal        @db.Decimal(20, 4) //入库数量
  batch_no         String         @db.VarChar(255) //批号
  note             String?        @db.Text //备注
  createdAt        DateTime       @default(now()) //创建时间
  updatedAt        DateTime       @updatedAt //更新时间
}

// 废料处理记录
model WasteProcessingRecord {
  id          Int      @id @default(autoincrement()) @db.Int //废料处理记录id
  materiel_id Int      @db.Int //物料id
  materiel    Materiel @relation(fields: [materiel_id], references: [id]) //物料
  quantity    Decimal  @db.Decimal(20, 4) //处理数量
  batch_no    String   @db.VarChar(255) //批号
  note        String?  @db.Text //备注
  createdAt   DateTime @default(now()) //创建时间
  updatedAt   DateTime @updatedAt //更新时间
}

// 消毒处理记录
model DisinfectionRecord {
  id          Int      @id @default(autoincrement()) @db.Int //消毒处理记录id
  materiel_id Int      @db.Int //物料id
  materiel    Materiel @relation(fields: [materiel_id], references: [id]) //物料
  quantity    Decimal  @db.Decimal(20, 4) //处理数量
  batch_no    String   @db.VarChar(255) //批号
  note        String?  @db.Text //备注
  createdAt   DateTime @default(now()) //创建时间
  updatedAt   DateTime @updatedAt //更新时间
}

// 库存盘点表
model StockCheck {
  id              Int              @id @default(autoincrement()) @db.Int //盘点id
  checkNo         String           @unique @db.VarChar(255) //盘点单号
  title           String           @db.VarChar(255) //盘点标题
  warehouse_id    Int              @db.Int //仓库id
  warehouse       Warehouse        @relation(fields: [warehouse_id], references: [id]) //仓库
  status          String           @default("draft") @db.VarChar(50) //状态：draft-草稿, checking-盘点中, completed-已完成, cancelled-已取消
  checkType       String           @default("full") @db.VarChar(50) //盘点类型：full-全盘, partial-抽盘
  plannedDate     DateTime //计划盘点日期
  actualDate      DateTime? //实际盘点日期
  checkUser_id    Int              @db.Int //盘点人id
  checkUser       User             @relation("StockCheckUser", fields: [checkUser_id], references: [id]) //盘点人
  approveUser_id  Int? //审核人id
  approveUser     User?            @relation("StockCheckApprover", fields: [approveUser_id], references: [id]) //审核人
  approvedAt      DateTime? //审核时间
  note            String?          @db.Text //备注
  totalItems      Int              @default(0) @db.Int //总盘点项数
  checkedItems    Int              @default(0) @db.Int //已盘点项数
  differenceItems Int              @default(0) @db.Int //差异项数
  items           StockCheckItem[] //盘点明细
  createdAt       DateTime         @default(now()) //创建时间
  updatedAt       DateTime         @updatedAt //更新时间
}

// 库存盘点明细表
model StockCheckItem {
  id                 Int        @id @default(autoincrement()) @db.Int //盘点明细id
  stockCheck_id      Int        @db.Int //盘点单id
  stockCheck         StockCheck @relation(fields: [stockCheck_id], references: [id], onDelete: Cascade) //盘点单
  materiel_id        Int        @db.Int //物料id
  materiel           Materiel   @relation(fields: [materiel_id], references: [id]) //物料
  batch_no           String     @db.VarChar(255) //批号
  location           String?    @db.VarChar(255) //库位
  systemQuantity     Decimal    @db.Decimal(20, 4) //系统数量
  actualQuantity     Decimal?   @db.Decimal(20, 4) //实盘数量
  differenceQuantity Decimal?   @db.Decimal(20, 4) //差异数量
  status             String     @default("pending") @db.VarChar(50) //状态：pending-待盘点, checked-已盘点, adjusted-已调整
  note               String?    @db.Text //备注
  checkUser_id       Int? //盘点人id
  checkUser          User?      @relation(fields: [checkUser_id], references: [id]) //盘点人
  checkedAt          DateTime? //盘点时间
  createdAt          DateTime   @default(now()) //创建时间
  updatedAt          DateTime   @updatedAt //更新时间
}

model UserFaceFeature {
  id             Int      @id @default(autoincrement())
  userId         Int      @unique
  user           User     @relation(fields: [userId], references: [id])
  faceDescriptor String   @db.Text
  faceImage      String   @db.Text
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([userId])
}

// 客户表
model Customer {
  id             Int                     @id @default(autoincrement()) @db.Int //客户id
  name           String                  @db.VarChar(255) //客户联系人姓名
  comname        String                  @db.VarChar(255) //客户公司名称
  email          String                  @db.VarChar(255) //客户邮箱
  tel            String                  @db.VarChar(255) //客户电话
  mobile         String                  @db.VarChar(255) //客户手机
  address        String                  @db.Text //客户地址
  fox            String                  @db.VarChar(255) //客户传真
  createAt       DateTime                @default(now()) //创建时间
  updateAt       DateTime                @updatedAt //更新时间
  user_id        Int                     @db.Int //创建用户id
  Admin          User                    @relation(fields: [user_id], references: [id]) //创建用户
  lock           Boolean                 @default(false) @db.TinyInt //客户锁定状态
  ReceivingInfo  CustomerReceivingInfo[] //客户收货信息
  InvoiceInfo    CustomerInvoiceInfo[] //客户发票信息
  SalesOutRecord SalesOutRecord[] //销售出库记录
}

// 客户收货信息表
model CustomerReceivingInfo {
  id          Int      @id @default(autoincrement()) @db.Int //收货信息id
  customer_id Int      @db.Int //客户id
  customer    Customer @relation(fields: [customer_id], references: [id]) //客户
  recName     String   @db.VarChar(255) //收货人姓名
  recAddress  String   @db.VarChar(255) //收货地址
  recTel      String   @db.VarChar(255) //收货人电话
  createAt    DateTime @default(now()) //创建时间
  updateAt    DateTime @updatedAt //更新时间
  user_id     Int      @db.Int //创建用户id
  Admin       User     @relation(fields: [user_id], references: [id]) //创建用户

  @@map("customer_receivinginfo")
}

// 发票类型枚举
enum InvoiceType {
  common //普通发票
  special //专用发票
  electronic //电子发票
}

// 客户发票信息表
model CustomerInvoiceInfo {
  id          Int         @id @default(autoincrement()) @db.Int //发票信息id
  customer_id Int         @db.Int //客户id
  customer    Customer    @relation(fields: [customer_id], references: [id]) //客户
  title       String      @db.VarChar(255) //发票抬头
  taxNumber   String      @db.VarChar(255) //税号
  bankName    String?     @db.VarChar(255) //开户银行
  bankAccount String?     @db.VarChar(255) //银行账号
  address     String?     @db.VarChar(255) //地址
  tel         String?     @db.VarChar(255) //电话
  invoiceType InvoiceType @default(common) //发票类型
  createAt    DateTime    @default(now()) //创建时间
  updateAt    DateTime    @updatedAt //更新时间

  @@map("customer_invoiceinfo")
}

// 销售出库记录
model SalesOutRecord {
  id           Int       @id @default(autoincrement()) @db.Int //销售出库记录id
  customer_id  Int       @db.Int //客户id
  customer     Customer  @relation(fields: [customer_id], references: [id]) //客户
  materiel_id  Int       @db.Int //物料id
  materiel     Materiel  @relation(fields: [materiel_id], references: [id]) //物料
  warehouse_id Int       @db.Int //仓库id
  warehouse    Warehouse @relation(fields: [warehouse_id], references: [id]) //仓库
  quantity     Decimal   @db.Decimal(20, 4) //出库数量
  batch_no     String    @db.VarChar(255) //批号
  order_no     String    @db.VarChar(255) //订单编号
  note         String?   @db.Text //备注
  user_id      Int       @db.Int //操作用户id
  user         User      @relation(fields: [user_id], references: [id]) //操作用户
  createdAt    DateTime  @default(now()) //创建时间
  updatedAt    DateTime  @updatedAt //更新时间
}
