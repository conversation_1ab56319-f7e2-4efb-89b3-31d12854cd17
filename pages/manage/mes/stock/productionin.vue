<template>
  <a-card title="生产入库">
    <!-- 表格区域 -->
    <manage-base-table
      :columns="columns"
      :model="searchForm"
      :query="queryFn"
      ref="tableRef"
    >
      <template #searchBox>
        <a-form-item label="工单编号">
          <a-input
            v-model:value="searchForm.taskNo"
            placeholder="请输入工单编号"
            allow-clear
          ></a-input>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="resetSearch">重置</a-button>
          </a-space>
        </a-form-item>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-button
            type="primary"
            @click="handleProductionIn(record)"
            :disabled="record.status !== 2"
          >
            入库
          </a-button>
        </template>
      </template>
    </manage-base-table>

    <!-- 入库模态框 -->
    <a-modal
      v-model:open="productionInModalOpen"
      title="生产入库"
      @ok="handleProductionInSubmit"
      :confirmLoading="productionInModalLoading"
      width="800px"
    >
      <a-descriptions
        :title="`工单编号: ${productionInForm.taskNo}`"
        :column="3"
        bordered
      >
        <a-descriptions-item label="工单编号">
          {{ productionInForm.taskNo }}
        </a-descriptions-item>
        <a-descriptions-item label="产品名称">
          {{ productionInForm.productName }}
        </a-descriptions-item>
        <a-descriptions-item label="计划数量">
          {{ productionInForm.planQuantity }}
        </a-descriptions-item>
        <a-descriptions-item label="已完成数量">
          {{ productionInForm.completed_quantity }}
        </a-descriptions-item>
        <a-descriptions-item label="合格品数量">
          {{ productionInForm.qualified_quantity }}
        </a-descriptions-item>
        <a-descriptions-item label="不合格品数量">
          {{ productionInForm.unqualified_quantity }}
        </a-descriptions-item>
      </a-descriptions>
      <a-divider>主产品入库信息</a-divider>

      <a-form :model="productionInForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="入库仓库"
              name="warehouse_id"
              :rules="[{ required: true, message: '请选择入库仓库' }]"
            >
              <manage-warehouse-selector
                :type="['production', 'finished', 'sales']"
                v-model:value="productionInForm.warehouse_id"
              ></manage-warehouse-selector>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="批次号"
              name="batch_no"
              :rules="[{ required: true, message: '请输入批次号' }]"
            >
              <a-input
                v-model:value="productionInForm.batch_no"
                placeholder="请输入批次号"
              ></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="实际入库数量"
              name="qualified_quantity"
              :rules="[
                { required: true, message: '请输入实际入库数量' },
                { type: 'number', min: 1, message: '数量必须大于0' },
              ]"
            >
              <a-input-number
                v-model:value="productionInForm.qualified_quantity"
                :min="1"
                style="width: 100%"
                @change="handleQualifiedQuantityChange"
              ></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备注" name="note">
              <a-textarea
                v-model:value="productionInForm.note"
                placeholder="请输入备注信息"
                :rows="2"
              ></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 不合格品入库信息 -->
      <template v-if="productionInForm.unqualified_quantity > 0">
        <a-divider>不合格品入库信息</a-divider>
        <a-form :model="productionInForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="废品库" name="waste_warehouse_id">
                <manage-warehouse-selector
                  :type="['waste']"
                  v-model:value="productionInForm.waste_warehouse_id"
                ></manage-warehouse-selector>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="废品批次号" name="waste_batch_no">
                <a-input
                  v-model:value="productionInForm.waste_batch_no"
                  placeholder="请输入废品批次号"
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="废品入库数量" name="waste_quantity">
                <a-input-number
                  v-model:value="productionInForm.waste_quantity"
                  :min="0"
                  :max="productionInForm.unqualified_quantity"
                  style="width: 100%"
                ></a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-alert
                v-if="
                  productionInForm.waste_warehouse_id &&
                  productionInForm.waste_quantity > 0
                "
                message="将同时入库不合格品到废品库"
                type="info"
                show-icon
              />
            </a-col>
          </a-row>
        </a-form>
      </template>

      <!-- 副产物信息 -->
      <template v-if="byproducts.length > 0">
        <a-divider>副产物信息</a-divider>
        <a-table
          :dataSource="byproducts"
          :columns="byproductColumns"
          size="small"
          :pagination="false"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'warehouse_id'">
              <manage-warehouse-selector
                style="width: 100%"
                :type="['waste']"
                v-model:value="record.warehouse_id"
              ></manage-warehouse-selector>
            </template>
            <template v-if="column.key === 'batch_no'">
              <a-input
                v-model:value="record.batch_no"
                placeholder="请输入批次号"
              />
            </template>
            <template v-if="column.key === 'calculatedQuantity'">
              {{ calculateByproductQuantity(record) }}
            </template>
            <template v-if="column.key === 'actualQuantity'">
              <a-input-number
                v-model:value="record.quantity"
                :min="0"
                style="width: 100%"
              />
            </template>
          </template>
        </a-table>
      </template>
    </a-modal>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive } from "vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 表格列定义
  const columns = [
    { title: "工单编号", dataIndex: "code", key: "code" },
    {
      title: "产品名称",
      key: "productName",
      customRender: ({ record }: { record: any }) => {
        return record.Materiel?.name || "-";
      },
    },
    {
      title: "计划数量",
      dataIndex: "quantity",
      key: "quantity",
      customRender: ({ text }: { text: any }) => {
        return Number(text) || 0;
      },
    },
    {
      title: "已完成数量",
      dataIndex: "completed_quantity",
      key: "completed_quantity",
      customRender: ({ text }: { text: any }) => {
        return Number(text) || 0;
      },
    },
    {
      title: "计划开始时间",
      dataIndex: "startAt",
      key: "startAt",
      customRender: ({ text }: { text: any }) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
    },
    {
      title: "计划结束时间",
      dataIndex: "endAt",
      key: "endAt",
      customRender: ({ text }: { text: any }) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
    },
    { title: "状态", dataIndex: "status", key: "status" },
    { title: "操作", key: "action" },
  ];

  // 副产物表格列定义
  const byproductColumns = [
    { title: "副产物名称", dataIndex: "materiel_name", key: "materiel_name" },
    {
      title: "配方比例",
      dataIndex: "ratio",
      key: "ratio",
      customRender: ({ text }: { text: any }) => {
        return Number(text).toFixed(2);
      },
    },
    {
      title: "计算数量",
      key: "calculatedQuantity",
    },
    { title: "实际数量", key: "actualQuantity" },
    { title: "入库仓库", key: "warehouse_id" },
    { title: "批次号", key: "batch_no" },
  ];

  // 搜索表单
  const searchForm = reactive({
    taskNo: "",
  });

  // 入库表单
  const productionInForm = reactive({
    id: null as number | null,
    taskNo: "",
    productName: "",
    planQuantity: 0,
    completed_quantity: 0,
    qualified_quantity: 0,
    unqualified_quantity: 0,
    materiel_id: null as number | null,
    warehouse_id: undefined as number | undefined,
    quantity: 0,
    batch_no: "",
    note: "",
    // 废品入库相关字段
    waste_warehouse_id: undefined as number | undefined,
    waste_quantity: 0,
    waste_batch_no: "",
  });

  // 副产物数据
  const byproducts = ref<any[]>([]);
  const taskByproducts = ref<any[]>([]);

  // 模态框状态
  const productionInModalOpen = ref(false);
  const productionInModalLoading = ref(false);
  const tableRef = ref();

  // 仓库选项
  const warehouseOptions = ref<any[]>([]);

  // 查询函数
  const queryFn = useApiTrpc().admin.production.queryProductionTask.query;

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return "default";
      case 1:
        return "processing";
      case 2:
        return "processing";
      case 3:
        return "success";
      case 4:
        return "error";
      default:
        return "default";
    }
  };

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return "草稿";
      case 1:
        return "待领料";
      case 2:
        return "生产中";
      case 3:
        return "已完成";
      case 4:
        return "已终止";
      default:
        return "未知状态";
    }
  };

  // 处理搜索
  const handleSearch = () => {
    tableRef.value?.refresh();
  };

  // 重置搜索
  const resetSearch = () => {
    searchForm.taskNo = "";
    handleSearch();
  };

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const result =
        await useApiTrpc().admin.warehouse.queryWarehouseList.query({});
      if (result && result.data && result.data.result) {
        warehouseOptions.value = result.data.result;
      }
    } catch (error) {
      console.error("获取仓库列表失败", error);
      message.error("获取仓库列表失败");
    }
  };

  // 获取工单的副产物信息
  const fetchByproducts = async (materialId: number) => {
    try {
      // 从生产配方中获取副产物信息
      const result = await useApiTrpc().admin.bom.querySubBom.query({
        materielId: materialId,
      });

      if (result && result.data) {
        taskByproducts.value = (result.data.result || []).map((item) => ({
          byproduct_material_id: item.childId,
          byproduct_material_name: item.child?.name || "",
          ratio: item.quantity || 0,
        }));

        // 初始化副产物数据
        byproducts.value = taskByproducts.value.map((item) => {
          return {
            materiel_id: item.byproduct_material_id,
            materiel_name: item.byproduct_material_name,
            ratio: item.ratio,
            quantity: calculateProductQuantity(
              item.ratio,
              productionInForm.qualified_quantity
            ),
            warehouse_id: productionInForm.warehouse_id,
            batch_no: productionInForm.batch_no,
          };
        });
      }
    } catch (error) {
      console.error("获取副产物信息失败", error);
      message.error("获取副产物信息失败");
    }
  };

  // 计算副产物数量
  const calculateProductQuantity = (ratio: number, mainQuantity: number) => {
    return Math.round(ratio * mainQuantity * 100) / 100; // 保留两位小数并四舍五入
  };

  // 当主产品数量变化时，重新计算副产物数量
  const handleQualifiedQuantityChange = (value: number | string) => {
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(numValue)) return;

    byproducts.value = byproducts.value.map((item) => {
      const byproductInfo = taskByproducts.value.find(
        (bp) => bp.byproduct_material_id === item.materiel_id
      );

      if (byproductInfo) {
        return {
          ...item,
          quantity: calculateProductQuantity(byproductInfo.ratio, numValue),
        };
      }
      return item;
    });
  };

  // 在表格中计算并显示副产物数量
  const calculateByproductQuantity = (record: any) => {
    const byproductInfo = taskByproducts.value.find(
      (bp) => bp.byproduct_material_id === record.materiel_id
    );

    if (byproductInfo) {
      return calculateProductQuantity(
        byproductInfo.ratio,
        productionInForm.qualified_quantity
      );
    }
    return 0;
  };

  // 处理生产入库
  const handleProductionIn = async (record: any) => {
    productionInForm.id = record.id;
    productionInForm.taskNo = record.code;
    productionInForm.productName = record.Materiel?.name || "";
    productionInForm.planQuantity = Number(record.quantity) || 0;
    productionInForm.completed_quantity =
      Number(record.completed_quantity) || 0;
    productionInForm.qualified_quantity =
      Number(record.qualified_quantity) || 0;
    productionInForm.unqualified_quantity =
      Number(record.unqualified_quantity) || 0;
    productionInForm.materiel_id = record.materiel_id;
    productionInForm.warehouse_id = undefined;

    // 生成批次号
    const batchPrefix = dayjs().format("YYYYMMDD");
    const batchSuffix = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");
    const batchNo = `${batchPrefix}-${batchSuffix}`;

    productionInForm.batch_no = batchNo;
    productionInForm.quantity = Number(record.quantity) || 1;
    productionInForm.note = "";

    // 废品入库相关字段初始化
    productionInForm.waste_warehouse_id = undefined;
    productionInForm.waste_quantity = productionInForm.unqualified_quantity;
    productionInForm.waste_batch_no = `W-${batchNo}`; // 废品批次号前缀为W-

    await fetchWarehouses();

    // 获取副产物信息
    if (record.materiel_id) {
      await fetchByproducts(record.materiel_id);
    }

    productionInModalOpen.value = true;
  };

  // 处理入库提交
  const handleProductionInSubmit = async () => {
    try {
      productionInModalLoading.value = true;

      // 表单验证
      if (!productionInForm.warehouse_id) {
        message.error("请选择入库仓库");
        productionInModalLoading.value = false;
        return;
      }

      if (!productionInForm.batch_no) {
        message.error("请输入批次号");
        productionInModalLoading.value = false;
        return;
      }

      if (
        !productionInForm.qualified_quantity ||
        productionInForm.qualified_quantity <= 0
      ) {
        message.error("请输入有效的入库数量");
        productionInModalLoading.value = false;
        return;
      }

      // 验证废品入库信息
      if (
        productionInForm.unqualified_quantity > 0 &&
        productionInForm.waste_quantity > 0 &&
        productionInForm.waste_warehouse_id
      ) {
        if (!productionInForm.waste_batch_no) {
          message.error("请输入废品批次号");
          productionInModalLoading.value = false;
          return;
        }

        if (
          productionInForm.waste_quantity >
          productionInForm.unqualified_quantity
        ) {
          message.error("废品入库数量不能大于不合格品数量");
          productionInModalLoading.value = false;
          return;
        }
      }

      // 验证副产物
      let isValid = true;
      byproducts.value.forEach((item, index) => {
        if (!item.warehouse_id) {
          message.error(`请为第${index + 1}个副产物选择入库仓库`);
          isValid = false;
          return;
        }

        if (!item.batch_no) {
          message.error(`请为第${index + 1}个副产物输入批次号`);
          isValid = false;
          return;
        }
      });

      if (!isValid) {
        productionInModalLoading.value = false;
        return;
      }

      // 提交主产品入库
      await useApiTrpc().admin.stock.stockIn.mutate({
        materiel_id: productionInForm.materiel_id!,
        warehouse_id: productionInForm.warehouse_id!,
        batch_no: productionInForm.batch_no,
        quantity: productionInForm.qualified_quantity,
        note: productionInForm.note,
      });

      // 创建生产入库记录
      await useApiTrpc().admin.production.createProductionInRecord.mutate({
        productionTaskId: productionInForm.id!,
        materiel_id: productionInForm.materiel_id!,
        quantity: productionInForm.qualified_quantity,
        batch_no: productionInForm.batch_no,
        note:
          productionInForm.note ||
          `生产工单${productionInForm.taskNo}的入库记录`,
      });

      // 提交废品入库（如果有）
      if (
        productionInForm.unqualified_quantity > 0 &&
        productionInForm.waste_quantity > 0 &&
        productionInForm.waste_warehouse_id
      ) {
        await useApiTrpc().admin.stock.stockIn.mutate({
          materiel_id: productionInForm.materiel_id!,
          warehouse_id: productionInForm.waste_warehouse_id,
          batch_no: productionInForm.waste_batch_no,
          quantity: productionInForm.waste_quantity,
          note: `生产工单${productionInForm.taskNo}的不合格品`,
        });
      }

      //完成工单
      await useApiTrpc().admin.production.updateProductionTask.mutate({
        id: productionInForm.id!,
        status: 3,
      });

      // 提交副产物入库
      if (byproducts.value.length > 0) {
        const byProductPromises = byproducts.value.map((item) =>
          useApiTrpc().admin.stock.stockIn.mutate({
            materiel_id: item.materiel_id,
            warehouse_id: item.warehouse_id,
            batch_no: item.batch_no,
            quantity: item.quantity,
            note: `生产工单${productionInForm.taskNo}的副产物`,
          })
        );

        await Promise.all(byProductPromises);
      }

      message.success("生产入库成功");
      productionInModalOpen.value = false;
      tableRef.value?.query();
    } catch (error: any) {
      console.error("生产入库失败", error);
      message.error(error?.message || "生产入库失败，请重试");
    } finally {
      productionInModalLoading.value = false;
    }
  };

  // 页面初始化时获取仓库列表
  onMounted(() => {
    fetchWarehouses();
  });
</script>
