<script lang="ts" setup>
  /**
   * 使用Ant Design Table,实现系统中通用的表格
   *
   * 列配置额外支持以下属性:
   * - defaultVisible: 设置为false时该列默认不显示，用户可以通过列设置勾选显示，默认值为true
   */
  import type {
    PaginationProps,
    TableColumnProps,
    TableProps,
  } from "ant-design-vue";
  import type { NitroFetchRequest } from "nitropack";
  import type { UnwrapRef } from "vue";
  import { SettingOutlined } from "@ant-design/icons-vue";
  import { ReloadOutlined, SearchOutlined } from "@ant-design/icons-vue";
  import { object } from "zod";

  const attrs = useAttrs();
  const filteredAttrs = computed(() => {
    return {
      ...attrs,
      columns: undefined,
      dataSource: undefined,
      pagination: undefined,
      change: undefined,
      loading: undefined,
    };
  });
  const dataSource = defineModel<any[]>("dataSource", {
    default: (): any[] => [],
  });
  const loading = defineModel("loading", {
    default: () => false,
  });
  const current = defineModel("current", {
    default: () => 1,
  });
  const total = defineModel("total", {
    default: () => 0,
  });
  const config = useRuntimeConfig();
  const pageSize = config.public.tableSize as number;
  const showTotal = (total: number, range: [number, number]) => {
    return `第 ${range[0]}-${range[1]} 条，共 ${total} 条`;
  };
  const pagination = reactive({
    total,
    current,
    pageSize,
    showTotal,
  }) as PaginationProps;
  const slots = useSlots() as Record<string, any>;
  type QueryType = (
    params?: any
  ) => Promise<{ data?: { result: any[]; total: number } }>;
  const model = defineModel("model", {
    default: () => ({}),
    type: Object as PropType<UnwrapRef<any>>,
  });
  const quickSearchInput = ref("");
  const props = defineProps({
    columns: {
      type: Array as PropType<ExtendedTableColumnProps<any>[]>,
      required: true,
    },
    query: {
      type: Function as PropType<QueryType>,
      // required: true,
    },
    refreshButton: {
      type: Boolean,
      default: true,
    },
    customColumns: {
      type: Boolean,
      default: true,
    },
    quickSearch: {
      type: Boolean,
      default: false,
    },
    quickSearchPlaceholder: {
      type: String,
      default: "",
    },
  });

  // 添加列显示控制相关状态
  const visibleColumns = ref<Record<string, boolean>>({});
  const columnsPopoverOpen = ref(false);
  // 修改为使用Popover控制搜索表单
  const searchPopoverOpen = ref(false);

  // 初始化所有列为可见
  onMounted(() => {
    initVisibleColumns();
  });

  // 初始化列显示状态
  const initVisibleColumns = () => {
    const columnsState: Record<string, boolean> = {};
    if (props.columns) {
      props.columns.forEach((column) => {
        if (column.key || column.dataIndex) {
          const key = String(column.key || column.dataIndex);
          // 检查列是否有defaultVisible属性，如果有则使用它的值，否则默认为true
          columnsState[key] = column.defaultVisible !== false;
        }
      });
    }
    visibleColumns.value = columnsState;
  };

  // 监听列变化
  watch(
    () => props.columns,
    () => {
      if (props.columns) {
        const newState = { ...visibleColumns.value };
        let hasChanges = false;

        props.columns.forEach((column) => {
          if (column.key || column.dataIndex) {
            const key = String(column.key || column.dataIndex);
            if (newState[key] === undefined) {
              newState[key] = true;
              hasChanges = true;
            }
          }
        });

        if (hasChanges) {
          visibleColumns.value = newState;
        }
      }
    },
    { immediate: true }
  );

  // 计算出当前可见的列
  const displayColumns = computed(() => {
    if (!props.columns) return [];

    return props.columns.filter((column) => {
      if (column.key || column.dataIndex) {
        const key = String(column.key || column.dataIndex);
        return visibleColumns.value[key];
      }
      return true; // 没有key或dataIndex的列始终显示
    });
  });

  // 计算出当前有效的搜索条件
  interface FilterItem {
    key: string;
    title: string;
    value: string;
  }

  const activeFilters = computed<FilterItem[]>(() => {
    const result: FilterItem[] = [];
    if (!model.value) return result;

    for (const key in model.value) {
      const value = model.value[key];
      // 只显示有值的条件（排除空字符串、null、undefined等）
      if (value !== "" && value !== null && value !== undefined) {
        // 尝试查找列标题作为显示名称
        let title = key;
        const matchColumn = props.columns.find(
          (col) => col.dataIndex === key || col.key === key
        );
        if (matchColumn && matchColumn.title) {
          title = matchColumn.title as string;
        }

        result.push({
          key,
          title,
          value:
            typeof value === "object" ? JSON.stringify(value) : String(value),
        });
      }
    }

    return result;
  });

  // 清除特定搜索条件
  const clearFilter = (key: string) => {
    if (model.value && key in model.value) {
      model.value[key] = undefined;
      search();
    }
  };

  // 更新列的可见性
  const updateColumnVisibility = (key: string, isVisible: boolean) => {
    const newState = { ...visibleColumns.value };
    newState[key] = isVisible;
    visibleColumns.value = newState;
  };

  // 重置所有列为可见
  const resetColumnVisibility = () => {
    const newState = { ...visibleColumns.value };
    Object.keys(newState).forEach((key) => {
      newState[key] = true;
    });
    visibleColumns.value = newState;
  };

  const formRef = ref();
  //   const query = () => useApi().get()<any>(props.url);
  const query = () => {
    if (!props.query) {
    } else {
      loading.value = true;
      setTimeout(async () => {
        const _query = {
          ...model.value,
          quick:
            quickSearchInput.value == "" ? undefined : quickSearchInput.value,
          p: current.value,
          take: pageSize,
          skip: (current.value - 1) * pageSize,
        };
        const { data } = await props.query!(_query);
        dataSource.value = data?.result || [];
        total.value = data?.total || 0;
        loading.value = false;
      }, 500);
    }
  };
  const handleTableChange: TableProps["onChange"] = (
    pag: PaginationProps,
    filters: any,
    sorter: any
  ) => {
    current.value = pag.current || 1;
    // console.log("filters", filters);
    // console.log("sorter", sorter);
  };
  const search = () => {
    current.value = 1;
    query();
    searchPopoverOpen.value = false; // 关闭搜索弹出框
  };
  const reset = () => {
    formRef.value.resetFields();
    query();
    searchPopoverOpen.value = false; // 关闭搜索弹出框
  };
  watch(
    () => current.value,
    () => {
      // query();
      console.log("current change");
      query();
    },
    { immediate: true }
  );
  onMounted(async () => {
    useNuxtApp().$emitter.on("formSaveSuccessed", () => {
      query();
    });
  });
  defineExpose({
    query,
  });
  export type TableInstance = {
    query: () => void;
  };
</script>
<template>
  <div v-bind="$attrs">
    <div
      class="search-bar"
      style="margin-bottom: 16px"
      v-if="
        props.quickSearch ||
        $slots.searchBox ||
        props.refreshButton ||
        props.customColumns ||
        $slots.rightButtonArea
      "
    >
      <a-row :gutter="16" align="middle">
        <a-col :span="8">
          <a-input-search
            v-if="props.quickSearch"
            v-model:value="quickSearchInput"
            :placeholder="quickSearchPlaceholder"
            enter-button="搜索"
            allowClear
            :loading="loading"
            @search="search"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input-search>
        </a-col>
        <a-col :span="16" style="text-align: right">
          <a-space>
            <!-- 添加搜索按钮和弹出气泡 -->
            <a-popover
              v-if="$slots.searchBox"
              v-model:open="searchPopoverOpen"
              title="搜索条件"
              trigger="click"
              placement="bottomLeft"
              :overlay-style="{ width: '350px' }"
            >
              <template #content>
                <a-form
                  name="searchBoxForm"
                  ref="formRef"
                  layout="vertical"
                  :model="model.value"
                  style="max-height: 500px; overflow-y: auto"
                >
                  <slot name="searchBox"></slot>
                  <a-flex justify="end" style="margin-top: 16px" gap="small">
                    <a-button
                      @click="reset"
                      danger
                      :loading="loading"
                      size="small"
                    >
                      <template #icon>
                        <ReloadOutlined />
                      </template>
                      重置
                    </a-button>
                    <a-button
                      @click="search"
                      type="primary"
                      :loading="loading"
                      size="small"
                    >
                      <template #icon>
                        <SearchOutlined />
                      </template>
                      查询
                    </a-button>
                  </a-flex>
                </a-form>
              </template>
              <a-badge :count="activeFilters.length">
                <a-button>
                  <template #icon><FilterOutlined /></template>
                  高级搜索
                </a-button>
              </a-badge>
            </a-popover>
            <a-popover
              v-model:open="columnsPopoverOpen"
              title="选择显示列"
              trigger="click"
              placement="bottomRight"
              v-if="props.customColumns"
            >
              <template #content>
                <a-flex vertical>
                  <a-flex vertical style="width: 100%">
                    <template
                      v-for="col in props.columns"
                      :key="String(col.key || col.dataIndex)"
                    >
                      <div
                        v-if="col.key || col.dataIndex"
                        style="margin-bottom: 8px"
                      >
                        <a-checkbox
                          :checked="
                            visibleColumns[String(col.key || col.dataIndex)]
                          "
                          @change="
                            (e) =>
                              updateColumnVisibility(
                                String(col.key || col.dataIndex),
                                e.target.checked
                              )
                          "
                        >
                          {{ col.title }}
                        </a-checkbox>
                      </div>
                    </template>
                  </a-flex>
                  <a-divider style="margin: 8px 0" />
                  <a-button
                    type="primary"
                    size="small"
                    @click="resetColumnVisibility"
                    >重置</a-button
                  >
                </a-flex>
              </template>

              <a-button>
                <template #icon><SettingOutlined /></template>
                列设置
              </a-button>
            </a-popover>
            <a-button
              @click="query"
              :loading="loading"
              style="margin-right: 8px"
              v-if="props.refreshButton"
            >
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
            <slot name="rightButtonArea"></slot>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 添加当前筛选条件展示区域 -->
    <a-flex
      v-if="activeFilters.length > 0"
      style="margin-bottom: 16px; display: none"
      align="center"
      wrap="wrap"
    >
      <div style="margin-right: 8px; color: rgba(0, 0, 0, 0.45)">搜索条件:</div>
      <a-space size="small" wrap>
        <a-tag
          v-for="filter in activeFilters"
          :key="filter.key"
          closable
          @close="clearFilter(filter.key)"
          color="blue"
        >
          {{ filter.title }}: {{ filter.value }}
        </a-tag>
      </a-space>
      <a-button
        type="link"
        size="small"
        @click="reset"
        style="margin-left: 8px"
      >
        清除所有
      </a-button>
    </a-flex>

    <a-table
      v-bind="filteredAttrs"
      :columns="displayColumns"
      :data-source="dataSource"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="loading"
      bordered
      size="small"
    >
      <template #[slotName]="slotProps" v-for="(slot, slotName) in slots">
        <slot :name="slotName" v-bind="slotProps"></slot>
      </template>
    </a-table>
  </div>
</template>
<style scoped>
  .search-bar {
    background: #fafafa;
    padding: 8px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
  }
</style>
