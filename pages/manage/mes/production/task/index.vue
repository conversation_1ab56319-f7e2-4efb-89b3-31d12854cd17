<template>
  <a-card title="生产工单管理">
    <template #extra>
      <a-button type="primary" @click="handleCreateTask">新建工单</a-button>
    </template>
    <!-- 表格区域 -->
    <manage-base-table
      :columns="columns"
      :query="query"
      :model="searchForm"
      rowKey="id"
      quickSearch
      quickSearchPlaceholder="输入工单编号查询"
    >
      <template #searchBox>
        <a-form-item name="code" label="工单编号">
          <!-- <a-input
            v-model:value="searchForm.code"
            placeholder="请输入工单编号"
            allow-clear
          /> -->
          <manage-base-search-input
            v-model:value="searchForm.code"
          ></manage-base-search-input>
        </a-form-item>
        <a-form-item name="status" label="工单状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 200px"
          >
            <a-select-option
              v-for="item in statusOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="handleView(record)">查看</a-button>
            <!-- <a-button
              v-if="record.status === 0"
              type="link"
              @click="handleEdit(record)"
            >
              编辑
            </a-button> -->
            <a-popconfirm
              v-if="record.status === 0"
              title="确认发布该工单吗？"
              @confirm="handlePublish(record)"
            >
              <a-button type="link" style="color: #52c41a">发布</a-button>
            </a-popconfirm>
            <a-popconfirm
              v-if="record.status === 0"
              title="确认删除该工单吗？"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </manage-base-table>

    <!-- 工单详情模态框 -->
    <a-modal
      v-model:open="taskDetailModalVisible"
      title="工单详情"
      :width="900"
      :footer="null"
      @cancel="closeTaskDetailModal"
    >
      <a-spin :spinning="taskDetailLoading">
        <a-tabs v-model:activeKey="activeTabKey">
          <a-tab-pane key="basic" tab="基本信息">
            <a-descriptions
              :title="`工单编号: ${currentTask?.code || ''}`"
              bordered
              :column="2"
            >
              <a-descriptions-item label="工单编号">
                {{ currentTask?.code || "" }}
              </a-descriptions-item>
              <a-descriptions-item label="产品名称">
                {{ currentTask?.Materiel?.name || "" }}
              </a-descriptions-item>
              <a-descriptions-item label="计划数量">
                {{ currentTask?.quantity || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="完成数量">
                {{ currentTask?.completed_quantity || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="合格品数量">
                {{ currentTask?.qualified_quantity || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="不合格品数量">
                {{ currentTask?.unqualified_quantity || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="工单状态">
                <a-tag :color="getStatusColor(currentTask?.status)">
                  {{ getStatusText(currentTask?.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="计划开始时间">
                {{
                  currentTask?.startAt
                    ? dayjs(currentTask.startAt).format("YYYY-MM-DD")
                    : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="计划结束时间">
                {{
                  currentTask?.endAt
                    ? dayjs(currentTask.endAt).format("YYYY-MM-DD")
                    : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="创建人">
                {{ currentTask?.createUser?.name || "未知" }}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">
                {{
                  currentTask?.createAt
                    ? dayjs(currentTask.createAt).format("YYYY-MM-DD HH:mm:ss")
                    : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="生产员" :span="2">
                <div v-if="currentTask?.users && currentTask.users.length > 0">
                  <a-tag
                    v-for="userItem in currentTask.users"
                    :key="userItem.user_id"
                    color="blue"
                    style="margin-right: 8px; margin-bottom: 4px"
                  >
                    {{ userItem.user?.name || `用户${userItem.user_id}` }}
                  </a-tag>
                </div>
                <span v-else style="color: #999">暂未分配生产员</span>
              </a-descriptions-item>
              <a-descriptions-item label="备注" :span="2">
                {{ currentTask?.description || "无" }}
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>

          <a-tab-pane key="outbound" tab="领料情况">
            <a-table
              :columns="outboundColumns"
              :data-source="outboundRecords"
              :pagination="false"
              :loading="outboundLoading"
              rowKey="id"
            >
              <template #emptyText>
                <span>暂无领料记录</span>
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane key="report" tab="报工情况">
            <a-table
              :columns="reportColumns"
              :data-source="reportRecords"
              :pagination="false"
              :loading="reportLoading"
              rowKey="id"
            >
              <template #emptyText>
                <span>暂无报工记录</span>
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane key="inbound" tab="入库情况">
            <a-table
              :columns="inboundColumns"
              :data-source="inboundRecords"
              :pagination="false"
              :loading="inboundLoading"
              rowKey="id"
            >
              <template #emptyText>
                <span>暂无入库记录</span>
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane key="workers" tab="生产员信息">
            <div v-if="currentTask?.users && currentTask.users.length > 0">
              <a-row :gutter="[16, 16]">
                <a-col
                  v-for="userItem in currentTask.users"
                  :key="userItem.user_id"
                  :xs="24"
                  :sm="12"
                  :md="8"
                  :lg="6"
                >
                  <a-card
                    size="small"
                    :title="userItem.user?.name || `用户${userItem.user_id}`"
                  >
                    <a-descriptions size="small" :column="1">
                      <a-descriptions-item label="员工姓名">
                        {{ userItem.user?.name || "-" }}
                      </a-descriptions-item>
                      <a-descriptions-item label="员工工号">
                        {{ userItem.user?.code || "-" }}
                      </a-descriptions-item>
                      <a-descriptions-item label="员工状态">
                        <a-tag
                          :color="
                            userItem.user?.status === 'active' ? 'green' : 'red'
                          "
                        >
                          {{
                            userItem.user?.status === "active" ? "正常" : "锁定"
                          }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="分配时间">
                        {{
                          dayjs(currentTask.createAt).format(
                            "YYYY-MM-DD HH:mm:ss"
                          )
                        }}
                      </a-descriptions-item>
                      <a-descriptions-item label="报工次数">
                        {{ getUserReportCount(userItem.user_id) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="累计报工数量">
                        {{ getUserReportQuantity(userItem.user_id) }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
              </a-row>
            </div>
            <a-empty v-else description="暂未分配生产员" />
          </a-tab-pane>
        </a-tabs>
      </a-spin>

      <div style="text-align: right; margin-top: 16px">
        <a-button @click="closeTaskDetailModal">关闭</a-button>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import { message, type TableColumnProps } from "ant-design-vue";
  import dayjs from "dayjs";
  import { useRouter } from "vue-router";

  // 新增router
  const router = useRouter();

  // 工单详情模态框相关状态
  const taskDetailModalVisible = ref(false);
  const taskDetailLoading = ref(false);
  const activeTabKey = ref("basic");
  const currentTask = ref<any>(null);

  // 领料记录相关状态
  const outboundColumns = [
    { title: "物料编码", dataIndex: ["materiel", "code"], key: "materielCode" },
    { title: "物料名称", dataIndex: ["materiel", "name"], key: "materielName" },
    { title: "批号", dataIndex: "batch_no", key: "batchNo" },
    { title: "领料数量", dataIndex: "quantity", key: "quantity" },
    { title: "备注", dataIndex: "note", key: "note" },
    {
      title: "领料时间",
      dataIndex: "createdAt",
      key: "createdAt",
      customRender: ({ text }: { text: string }) =>
        text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
  ];
  const outboundRecords = ref<any[]>([]);
  const outboundLoading = ref(false);

  // 报工记录相关状态
  const reportColumns = [
    {
      title: "合格品数量",
      dataIndex: "quantity",
      key: "quantity",
      width: 120,
      customRender: ({ text }: { text: any }) => (text ? Number(text) : 0),
    },
    {
      title: "不合格品数量",
      dataIndex: "unqualified_quantity",
      key: "unqualified_quantity",
      width: 120,
      customRender: ({ text }: { text: any }) => (text ? Number(text) : 0),
    },
    {
      title: "报工人员",
      dataIndex: ["user", "name"],
      key: "userName",
      width: 120,
      customRender: ({ record }: { record: any }) => {
        return record.user?.name || `用户${record.user_id}`;
      },
    },
    {
      title: "员工工号",
      dataIndex: ["user", "code"],
      key: "userCode",
      width: 120,
      customRender: ({ record }: { record: any }) => {
        return record.user?.code || "-";
      },
    },
    {
      title: "备注",
      dataIndex: "note",
      key: "note",
      ellipsis: true,
    },
    {
      title: "报工时间",
      dataIndex: "createAt",
      key: "createAt",
      width: 180,
      customRender: ({ text }: { text: string }) =>
        text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
  ];
  const reportRecords = ref<any[]>([]);
  const reportLoading = ref(false);

  // 入库记录相关状态
  const inboundColumns = [
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      key: "materielName",
    },
    { title: "批次号", dataIndex: "batch_no", key: "batchNo" },
    {
      title: "入库数量",
      dataIndex: "quantity",
      key: "quantity",
      customRender: ({ text }: { text: any }) => (text ? Number(text) : 0),
    },
    { title: "备注", dataIndex: "note", key: "note" },
    {
      title: "入库时间",
      dataIndex: "createdAt",
      key: "createdAt",
      customRender: ({ text }: { text: string }) =>
        text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
  ];
  const inboundRecords = ref<any[]>([]);
  const inboundLoading = ref(false);

  // 获取指定用户的报工次数
  const getUserReportCount = (userId: number) => {
    return reportRecords.value.filter((record) => record.user_id === userId)
      .length;
  };

  // 获取指定用户的累计报工数量
  const getUserReportQuantity = (userId: number) => {
    return reportRecords.value
      .filter((record) => record.user_id === userId)
      .reduce((total, record) => total + Number(record.quantity || 0), 0);
  };

  // 关闭工单详情模态框
  const closeTaskDetailModal = () => {
    taskDetailModalVisible.value = false;
    currentTask.value = null;
    outboundRecords.value = [];
    reportRecords.value = [];
    inboundRecords.value = [];
  };

  // 加载工单详情数据
  const loadTaskDetails = async (taskId: number) => {
    taskDetailLoading.value = true;

    try {
      // 1. 加载工单基本信息
      const taskResult =
        await useApiTrpc().admin.production.queryProductionTask.query({
          includeUsers: true,
          take: 100,
          skip: 0,
        });

      if (taskResult && taskResult.code === 1 && taskResult.data) {
        const taskList = taskResult.data.result || [];
        const task = taskList.find((item: any) => item.id === taskId);

        if (task) {
          currentTask.value = task;
        }
      }

      // 2. 加载领料记录
      outboundLoading.value = true;
      const outboundResult =
        await useApiTrpc().admin.production.queryProductionOutRecords.query({
          taskId: taskId,
          take: 100,
          skip: 0,
        });

      if (outboundResult && outboundResult.code === 1) {
        outboundRecords.value = outboundResult.data.result || [];
      }
      outboundLoading.value = false;

      // 3. 加载报工记录
      reportLoading.value = true;
      try {
        // 使用新添加的API获取报工记录
        const reportResult =
          await useApiTrpc().admin.production.getProductionReports.query({
            taskId: taskId,
          });

        if (reportResult && reportResult.code === 1) {
          reportRecords.value = reportResult.data || [];
        } else {
          reportRecords.value = [];
        }
      } catch (error) {
        console.error("加载报工记录失败:", error);
        reportRecords.value = [];
      }
      reportLoading.value = false;

      // 4. 加载入库记录
      inboundLoading.value = true;
      try {
        // 使用新添加的API获取入库记录
        const inboundResult =
          await useApiTrpc().admin.production.getProductionInRecords.query({
            taskId: taskId,
          });

        if (inboundResult && inboundResult.code === 1) {
          inboundRecords.value = inboundResult.data || [];
        } else {
          inboundRecords.value = [];
        }
      } catch (error) {
        console.error("加载入库记录失败:", error);
        inboundRecords.value = [];
      }
      inboundLoading.value = false;
    } catch (error) {
      console.error("加载工单详情失败:", error);
      message.error("加载工单详情失败");
    } finally {
      taskDetailLoading.value = false;
    }
  };

  // 状态选项
  const statusOptions = [
    { value: "draft", label: "草稿" },
    { value: "pending", label: "待生产" },
    { value: "in_progress", label: "生产中" },
    { value: "completed", label: "已完成" },
    { value: "cancelled", label: "已取消" },
  ];
  type QueryParams = Parameters<typeof query>[0];
  // 表格列定义
  const columns: TableColumnProps<any>[] = [
    {
      title: "工单编号",
      dataIndex: "code",
      width: 180,
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      width: 180,
      customRender(opt) {
        return opt.record.Materiel.name;
      },
    },
    {
      title: "计划数量",
      dataIndex: "quantity",
      width: 120,
    },
    {
      title: "完成数量",
      dataIndex: "completed_quantity",
      width: 120,
    },
    {
      title: "创建人",
      dataIndex: "createUser",
      width: 120,
      customRender(opt) {
        return opt.record.createUser?.name || "未知";
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 120,
    },
    {
      title: "开始时间-结束时间",
      dataIndex: "startTime",
      customRender(opt) {
        return `${dayjs(opt.record.startAt).format("YYYY-MM-DD")} - ${dayjs(
          opt.record.endAt
        ).format("YYYY-MM-DD")}`;
      },
    },
    {
      title: "操作",
      key: "action",
      width: 200,
    },
  ];

  // 搜索表单
  const searchForm = reactive<QueryParams>({});

  // TRPC查询
  const query = useApiTrpc().admin.production.queryProductionTask.query;

  // 搜索方法
  const handleSearch = () => {
    // 触发表格刷新
  };

  // 重置搜索
  const resetSearch = () => {
    searchForm.code = "";
    searchForm.status = undefined;
    handleSearch();
  };

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    const map: Record<number, string> = {
      0: "default",
      1: "warning",
      2: "processing",
      3: "success",
      4: "error",
    };
    return map[status] || "default";
  };

  // 获取状态文本
  const getStatusText = (status: number) => {
    const map: Record<number, string> = {
      0: "草稿",
      1: "待生产",
      2: "生产中",
      3: "已完成",
      4: "已取消",
    };
    return map[status] || "未知";
  };

  // 新建工单
  const handleCreateTask = () => {
    router.push("/manage/mes/production/task/create");
  };

  // 查看工单
  const handleView = async (record: any) => {
    // 设置当前选中的工单
    currentTask.value = record;
    // 打开模态框
    taskDetailModalVisible.value = true;
    // 加载工单详情数据
    await loadTaskDetails(record.id);
  };

  // 编辑工单
  const handleEdit = (record: any) => {
    router.push(`/manage/mes/production/task/create?id=${record.id}`);
  };

  // 删除工单
  const handleDelete = async (record: any) => {
    try {
      await useApiTrpc().admin.production.deleteProductionTask.mutate({
        id: record.id,
      });
      message.success("删除成功");
      handleSearch(); // 刷新列表
    } catch (error) {
      console.error("删除工单失败:", error);
      message.error("删除失败");
    }
  };

  // 发布工单
  const handlePublish = async (record: any) => {
    try {
      await useApiTrpc().admin.production.updateProductionTask.mutate({
        id: record.id,
        status: 1, // 1代表"待生产"状态
      });
      message.success("工单发布成功");
      handleSearch(); // 刷新列表
    } catch (error) {
      console.error("发布工单失败:", error);
      message.error("发布失败");
    }
  };
</script>

<style scoped>
  .production-task {
    padding: 20px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .search-area {
    margin-bottom: 20px;
  }

  .table-area {
    margin-bottom: 20px;
  }
</style>
