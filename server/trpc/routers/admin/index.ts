import { router } from "@/server/trpc";
import * as userRouter from "./user";
import * as warehouseRouter from "./warehouse";
import * as productionRouter from "./production";
import * as materielRouter from "./materiel";
import * as stockRouter from "./stock";
import * as purchaseRouter from "./purchase";
import * as bomRouter from "./bom";
import * as authRouter from "./auth";
import * as customerRouter from "./customer";
import * as stockcheckRouter from "./stockcheck";
export const adminRouter = router({
  user: router(userRouter),
  warehouse: router(warehouseRouter),
  production: router(productionRouter),
  materiel: router(materielRouter),
  stock: router(stockRouter),
  purchase: router(purchaseRouter),
  bom: router(bomRouter),
  auth: router(authRouter),
  customer: router(customerRouter),
  stockcheck: router(stockcheckRouter),
});
