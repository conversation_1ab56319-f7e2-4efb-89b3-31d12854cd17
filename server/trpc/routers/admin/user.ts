import {
  router,
  mergeRouters,
  singleResultProcedure,
  tableResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { UserStatus } from "@prisma/client";
import { useApiFace } from "@/server/utils/face";
import CryptoJS from "crypto-js";

export const queryUser = singleResultProcedure
  .meta({ permission: ["user:query"], authRequired: true })
  .query(async () => {
    const users = await prisma.user.findManyWithCount({
      include: {
        role: {
          select: {
            name: true,
          },
        },
      },
      omit: {
        password: true,
      },
    });
    return { code: 1, message: "success", data: users };
  });

export const updateUser = singleResultProcedure
  .meta({ permission: ["user:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      name: z.string(),
      email: z.string().optional(),
      role_id: z.number(),
      status: z.nativeEnum(UserStatus),
    })
  )
  .mutation(async ({ input }) => {
    const user = await prisma.user.update({
      where: { id: input.id },
      data: input,
    });
    return { code: 1, message: "success", data: user };
  });

export const createUser = singleResultProcedure
  .meta({ permission: ["user:create"], authRequired: true })
  .input(
    z.object({
      username: z.string(),
      password: z.string(),
      name: z.string(),
      email: z.string().optional(),
      role_id: z.number(),
      status: z.nativeEnum(UserStatus),
    })
  )
  .mutation(async ({ input }) => {
    const user = await prisma.user.create({
      data: input,
    });
    return { code: 1, message: "success", data: user };
  });

export const queryRole = tableResultProcedure
  .meta({ permission: ["role:query"], authRequired: true })
  .query(async ({ input }) => {
    const roles = await prisma.role.findManyWithCount({
      take: input.take,
      skip: input.skip,
    });
    return { code: 1, message: "success", data: roles };
  });

export const createRole = singleResultProcedure
  .meta({ permission: ["role:create"], authRequired: true })
  .input(
    z.object({
      name: z.string(),
      description: z.string().optional(),
      status: z.enum(["active", "inactive"]),
    })
  )
  .mutation(async ({ input }) => {
    const role = await prisma.role.create({
      data: input,
    });
    return { code: 1, message: "success", data: role };
  });

export const updateRole = singleResultProcedure
  .meta({ permission: ["role:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      name: z.string(),
      description: z.string().optional(),
      status: z.enum(["active", "inactive"]),
    })
  )
  .mutation(async ({ input }) => {
    const role = await prisma.role.update({
      where: { id: input.id },
      data: input,
    });
    return { code: 1, message: "success", data: role };
  });

export const deleteRole = singleResultProcedure
  .meta({ permission: ["role:delete"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .mutation(async ({ input }) => {
    const role = await prisma.role.delete({
      where: { id: input.id },
    });
    return { code: 1, message: "success", data: role };
  });

// 定义保存人脸特征的输入schema
const saveFaceFeatureSchema = z.object({
  userId: z.number(),
  username: z.string(),
  faceDescriptor: z.array(z.number()),
  faceImage: z.string(),
});

export const saveFaceFeature = singleResultProcedure
  .meta({ permission: ["user:update"], authRequired: true })
  .input(saveFaceFeatureSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { userId, username, faceDescriptor, faceImage } = input;

      // 将人脸特征数组转换为JSON字符串
      const faceDescriptorJson = JSON.stringify(faceDescriptor);
      const { addFace } = useApiFace();
      const res = await addFace(username, faceImage);
      // 保存人脸特征到数据库
      await prisma.userFaceFeature.upsert({
        where: {
          userId: userId,
        },
        update: {
          faceDescriptor: faceDescriptorJson,
          faceImage: faceImage,
          updatedAt: new Date(),
        },
        create: {
          userId: userId,
          faceDescriptor: faceDescriptorJson,
          faceImage: faceImage,
        },
      });

      return {
        code: 1,
        message: "人脸特征保存成功",
      };
    } catch (error) {
      console.error("Save face feature error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "保存人脸特征失败",
      });
    }
  });
export const getFaceFeature = singleResultProcedure
  .meta({ permission: ["user:query"], authRequired: true })
  .input(z.object({ userId: z.number() }))
  .query(async ({ input }) => {
    const { userId } = input;
    const faceFeature = await prisma.userFaceFeature.findUnique({
      where: {
        userId: userId,
      },
    });
    return { code: 1, message: "success", data: faceFeature };
  });

// 获取角色菜单
export const getRoleMenus = singleResultProcedure
  .meta({ permission: ["role:query"], authRequired: true })
  .input(z.object({ roleId: z.number() }))
  .query(async ({ input }) => {
    const roleMenus = await prisma.role.findFirst({
      where: {
        id: input.roleId,
      },
      select: {
        Menu: true,
      },
    });
    return { code: 1, message: "success", data: roleMenus };
  });

// 更新角色菜单
export const updateRoleMenus = singleResultProcedure
  .meta({ permission: ["role:update"], authRequired: true })
  .input(
    z.object({
      roleId: z.number(),
      menuNames: z.array(z.string()),
    })
  )
  .mutation(async ({ input }) => {
    await prisma.role.update({
      data: {
        Menu: input.menuNames,
      },
      where: {
        id: input.roleId,
      },
    });
    // 先删除原有的角色菜单关联
    // await prisma.roleMenu.deleteMany({
    //   where: { role_id: input.roleId },
    // });

    // // 获取所有菜单ID
    // const menus = await prisma.menu.findMany({
    //   where: {
    //     name: {
    //       in: input.menuNames,
    //     },
    //   },
    // });

    // // 创建新的角色菜单关联
    // await prisma.roleMenu.createMany({
    //   data: menus.map((menu) => ({
    //     role_id: input.roleId,
    //     menu_id: menu.id,
    //   })),
    // });

    return { code: 1, message: "success" };
  });
// 修改密码
export const changePassword = singleResultProcedure
  .meta({ permission: ["user:update"], authRequired: true })
  .input(
    z.object({
      oldPassword: z.string().min(1, "请输入原密码"),
      newPassword: z.string().min(6, "新密码至少6位"),
    })
  )
  .mutation(async ({ input, ctx }) => {
    const userId = ctx.user?.id;
    if (!userId) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "未登录" });
    }
    const user = await prisma.user.findUnique({ where: { id: userId } });
    if (!user) {
      throw new TRPCError({ code: "NOT_FOUND", message: "用户不存在" });
    }
    // 校验原密码
    const oldPwdHash = CryptoJS.SHA256(input.oldPassword).toString();
    if (user.password !== oldPwdHash) {
      // throw new TRPCError({ code: "BAD_REQUEST", message: "原密码错误" });
      return { code: 0, message: "原密码错误" };
    }
    // 更新新密码
    await prisma.user.update({
      where: { id: userId },
      data: { password: input.newPassword },
    });
    return { code: 1, message: "密码修改成功" };
  });
