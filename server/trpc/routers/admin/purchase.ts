import {
  router,
  mergeRouters,
  tableResultProcedure,
  singleResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";

export const querySupplier = tableResultProcedure
  .meta({ permission: ["supplier:query"], authRequired: true })
  .input(z.object({}))
  .query(async ({ input }) => {
    const supplier = await prisma.supplier.findManyWithCount({
      take: input.take,
      skip: input.skip,
    });
    return {
      code: 200,
      message: "success",
      data: supplier,
    };
  });

// 新增供应商接口
export const addSupplier = singleResultProcedure
  .meta({ permission: ["supplier:add"], authRequired: true })
  .input(
    z.object({
      name: z.string().min(1, "供应商名称不能为空"),
      citycode: z.array(z.string()),
      address: z.string().optional(),
      contactName: z.string().optional(),
      contactPhone: z.string().optional(),
      email: z.string().optional(),
      lock: z.boolean().default(false),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      // 检查是否已存在同名供应商
      const existingSupplier = await prisma.supplier.findFirst({
        where: {
          name: input.name,
        },
      });

      if (existingSupplier) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "已存在同名供应商",
        });
      }

      // 创建新供应商
      const supplier = await prisma.supplier.create({
        data: {
          name: input.name,
          citycode: input.citycode,
          address: input.address || "",
          contactName: input.contactName || "",
          contactPhone: input.contactPhone || "",
          email: input.email || "",
          lock: input.lock,
          user_id: ctx.user.id,
        },
      });

      return {
        code: 200,
        message: "添加供应商成功",
        data: {
          ...supplier,
          contactName: input.contactName || "",
          contactPhone: input.contactPhone || "",
          email: input.email || "",
        },
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "添加供应商失败",
        cause: error,
      });
    }
  });

// 更新供应商接口
export const updateSupplier = singleResultProcedure
  .meta({ permission: ["supplier:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      name: z.string().min(1, "供应商名称不能为空"),
      citycode: z.array(z.string()),
      address: z.string().optional(),
      contactName: z.string().optional(),
      contactPhone: z.string().optional(),
      email: z.string().optional(),
      lock: z.boolean(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      // 检查供应商是否存在
      const existingSupplier = await prisma.supplier.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!existingSupplier) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "供应商不存在",
        });
      }

      // 检查是否有同名但ID不同的供应商
      const duplicateSupplier = await prisma.supplier.findFirst({
        where: {
          name: input.name,
          id: {
            not: input.id,
          },
        },
      });

      if (duplicateSupplier) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "已存在同名供应商",
        });
      }

      // 更新供应商信息
      const supplier = await prisma.supplier.update({
        where: {
          id: input.id,
        },
        data: {
          name: input.name,
          citycode: input.citycode,
          address: input.address || "",
          lock: input.lock,
        },
      });

      return {
        code: 200,
        message: "更新供应商成功",
        data: {
          ...supplier,
          contactName: input.contactName || "",
          contactPhone: input.contactPhone || "",
          email: input.email || "",
        },
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新供应商失败",
        cause: error,
      });
    }
  });

// 切换供应商锁定状态
export const toggleSupplierLock = singleResultProcedure
  .meta({ permission: ["supplier:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      lock: z.boolean(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      // 检查供应商是否存在
      const existingSupplier = await prisma.supplier.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!existingSupplier) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "供应商不存在",
        });
      }

      // 更新供应商锁定状态
      const supplier = await prisma.supplier.update({
        where: {
          id: input.id,
        },
        data: {
          lock: input.lock,
        },
      });

      return {
        code: 200,
        message: input.lock ? "供应商已锁定" : "供应商已解锁",
        data: supplier,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新供应商状态失败",
        cause: error,
      });
    }
  });

// 删除供应商
export const deleteSupplier = singleResultProcedure
  .meta({ permission: ["supplier:delete"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查供应商是否存在
      const existingSupplier = await prisma.supplier.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!existingSupplier) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "供应商不存在",
        });
      }

      // 删除供应商
      await prisma.supplier.delete({
        where: {
          id: input.id,
        },
      });

      return {
        code: 200,
        message: "删除供应商成功",
        data: null,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除供应商失败",
        cause: error,
      });
    }
  });

// 采购订单相关接口
// 查询采购订单列表
export const queryPurchaseOrders = tableResultProcedure
  .meta({ permission: ["purchase:query"], authRequired: true })
  .input(
    z.object({
      orderNo: z.string().optional(),
      supplierName: z.string().optional(),
      status: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    })
  )
  .query(async ({ input, ctx }) => {
    try {
      const where: any = {};

      if (input.orderNo) {
        where.orderNo = {
          contains: input.orderNo,
        };
      }

      if (input.supplierName) {
        where.supplier = {
          name: {
            contains: input.supplierName,
          },
        };
      }

      if (input.status) {
        where.status = input.status;
      }

      if (input.startDate && input.endDate) {
        where.createdAt = {
          gte: new Date(input.startDate),
          lte: new Date(input.endDate),
        };
      }

      const result = await prisma.purchaseOrder.findManyWithCount({
        where,
        take: input.take,
        skip: input.skip,
        orderBy: {
          createdAt: "desc",
        },
        include: {
          supplier: {
            select: {
              name: true,
            },
          },
          items: true,
          user: {
            select: {
              username: true,
            },
          },
        },
      });

      return {
        code: 200,
        message: "success",
        data: result,
      };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取采购订单列表失败",
        cause: error,
      });
    }
  });

// 获取采购订单详情
export const getPurchaseOrder = singleResultProcedure
  .meta({ permission: ["purchase:query"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
    })
  )
  .query(async ({ input }) => {
    try {
      const order = await prisma.purchaseOrder.findUnique({
        where: {
          id: input.id,
        },
        include: {
          warehouse: true,
          supplier: true,
          items: {
            include: {
              material: true,
            },
          },
          user: {
            select: {
              username: true,
            },
          },
          receiptRecords: {
            include: {
              items: {
                include: {
                  orderItem: {
                    include: {
                      material: true,
                    },
                  },
                },
              },
              user: {
                select: {
                  username: true,
                },
              },
            },
          },
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "采购订单不存在",
        });
      }

      return {
        code: 200,
        message: "success",
        data: order,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取采购订单详情失败",
        cause: error,
      });
    }
  });

// 创建采购订单
export const createPurchaseOrder = singleResultProcedure
  .meta({ permission: ["purchase:add"], authRequired: true })
  .input(
    z.object({
      supplierId: z.number(),
      expectedDeliveryDate: z.string().optional(),
      warehouseId: z.number(),
      items: z.array(
        z.object({
          id: z.number(),
          quantity: z.number().positive(),
          price: z.number().nonnegative(),
          note: z.string().optional(),
        })
      ),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      // 检查供应商是否存在
      const supplier = await prisma.supplier.findUnique({
        where: {
          id: input.supplierId,
        },
      });

      if (!supplier) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "供应商不存在",
        });
      }

      // 检查供应商是否被锁定
      if (supplier.lock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "该供应商已被锁定，无法创建订单",
        });
      }

      // 生成订单编号
      const date = new Date();
      const dateString = date.toISOString().slice(0, 10).replace(/-/g, "");

      // 获取当天最大订单号
      const latestOrder = await prisma.purchaseOrder.findFirst({
        where: {
          orderNo: {
            startsWith: `PO-${dateString}`,
          },
        },
        orderBy: {
          orderNo: "desc",
        },
      });

      let orderNo;
      if (latestOrder) {
        const seq = parseInt(latestOrder.orderNo.split("-")[2]) + 1;
        orderNo = `PO-${dateString}-${seq.toString().padStart(3, "0")}`;
      } else {
        orderNo = `PO-${dateString}-001`;
      }

      // 计算订单总金额
      const totalAmount = input.items.reduce(
        (sum, item) => sum + item.quantity * item.price,
        0
      );

      // 创建采购订单
      const order = await prisma.purchaseOrder.create({
        data: {
          orderNo,
          supplierId: input.supplierId,
          totalAmount,
          expectedDeliveryDate: input.expectedDeliveryDate
            ? new Date(input.expectedDeliveryDate)
            : null,
          status: "draft", // 默认为草稿状态
          note: input.note || "",
          userId: ctx.user.id,
          warehouseId: input.warehouseId,
          items: {
            create: input.items.map((item) => ({
              materialId: item.id,
              quantity: item.quantity,
              unitPrice: item.price,
              note: item.note || "",
            })),
          },
        },
        include: {
          supplier: true,
          items: {
            include: {
              material: true,
            },
          },
        },
      });

      return {
        code: 200,
        message: "创建采购订单成功",
        data: order,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "创建采购订单失败",
        cause: error,
      });
    }
  });

// 更新采购订单
export const updatePurchaseOrder = singleResultProcedure
  .meta({ permission: ["purchase:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      supplierId: z.number(),
      expectedDeliveryDate: z.string().optional(),
      warehouseId: z.number(),
      items: z.array(
        z.object({
          id: z.number().optional(), // 存在表示更新，不存在表示新增
          materialId: z.number(),
          quantity: z.number().positive(),
          price: z.number().nonnegative(),
          note: z.string().optional(),
          deleted: z.boolean().optional(), // 标记是否删除
        })
      ),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      // 检查订单是否存在
      const existingOrder = await prisma.purchaseOrder.findUnique({
        where: {
          id: input.id,
        },
        include: {
          items: true,
        },
      });

      if (!existingOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "采购订单不存在",
        });
      }

      // 检查订单状态是否可以编辑
      if (!["draft", "rejected"].includes(existingOrder.status)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "当前订单状态不可编辑",
        });
      }

      // 检查供应商是否存在
      const supplier = await prisma.supplier.findUnique({
        where: {
          id: input.supplierId,
        },
      });

      if (!supplier) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "供应商不存在",
        });
      }

      // 检查供应商是否被锁定
      if (supplier.lock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "该供应商已被锁定，无法更新订单",
        });
      }

      // 计算订单总金额
      const totalAmount = input.items
        .filter((item) => !item.deleted)
        .reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);

      // 更新采购订单
      const order = await prisma.$transaction(async (prisma) => {
        // 更新订单基本信息
        const updatedOrder = await prisma.purchaseOrder.update({
          where: {
            id: input.id,
          },
          data: {
            supplierId: input.supplierId,
            warehouseId: input.warehouseId,
            totalAmount,
            expectedDeliveryDate: input.expectedDeliveryDate
              ? new Date(input.expectedDeliveryDate)
              : null,
            note: input.note || "",
          },
        });

        // 处理订单项
        for (const item of input.items) {
          if (item.id) {
            // 更新或删除现有订单项
            if (item.deleted) {
              await prisma.purchaseOrderItem.delete({
                where: {
                  id: item.id,
                },
              });
            } else {
              await prisma.purchaseOrderItem.update({
                where: {
                  id: item.id,
                },
                data: {
                  materialId: item.materialId,
                  quantity: item.quantity,
                  unitPrice: item.unitPrice,
                  note: item.note || "",
                },
              });
            }
          } else if (!item.deleted) {
            // 创建新订单项
            await prisma.purchaseOrderItem.create({
              data: {
                purchaseOrderId: input.id,
                materialId: item.materialId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                note: item.note || "",
              },
            });
          }
        }

        return updatedOrder;
      });

      // 获取更新后的完整订单信息
      const updatedOrder = await prisma.purchaseOrder.findUnique({
        where: {
          id: input.id,
        },
        include: {
          supplier: true,
          items: {
            include: {
              material: true,
            },
          },
        },
      });

      return {
        code: 200,
        message: "更新采购订单成功",
        data: updatedOrder,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新采购订单失败",
        cause: error,
      });
    }
  });

// 删除采购订单
export const deletePurchaseOrder = singleResultProcedure
  .meta({ permission: ["purchase:delete"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查订单是否存在
      const existingOrder = await prisma.purchaseOrder.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!existingOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "采购订单不存在",
        });
      }

      // 检查订单状态是否可以删除
      if (existingOrder.status !== "draft") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只有草稿状态的订单可以删除",
        });
      }

      // 删除采购订单及其关联项
      await prisma.$transaction([
        prisma.purchaseOrderItem.deleteMany({
          where: {
            purchaseOrderId: input.id,
          },
        }),
        prisma.purchaseOrder.delete({
          where: {
            id: input.id,
          },
        }),
      ]);

      return {
        code: 200,
        message: "删除采购订单成功",
        data: null,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除采购订单失败",
        cause: error,
      });
    }
  });

// 提交采购订单审核
export const submitPurchaseOrder = singleResultProcedure
  .meta({ permission: ["purchase:submit"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查订单是否存在
      const existingOrder = await prisma.purchaseOrder.findUnique({
        where: {
          id: input.id,
        },
        include: {
          items: true,
        },
      });

      if (!existingOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "采购订单不存在",
        });
      }

      // 检查订单状态是否可以提交审核
      if (existingOrder.status !== "draft") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只有草稿状态的订单可以提交审核",
        });
      }

      // 检查订单是否有订单项
      if (existingOrder.items.length === 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "订单必须包含至少一个物料项",
        });
      }

      // 更新订单状态为待审核
      const updatedOrder = await prisma.purchaseOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "pending",
          submittedAt: new Date(),
        },
      });

      return {
        code: 200,
        message: "提交采购订单审核成功",
        data: updatedOrder,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "提交采购订单审核失败",
        cause: error,
      });
    }
  });

// 审批采购订单
export const approvePurchaseOrder = singleResultProcedure
  .meta({ permission: ["purchase:approve"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      approved: z.boolean(),
      reason: z.string().optional(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      // 检查订单是否存在
      const existingOrder = await prisma.purchaseOrder.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!existingOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "采购订单不存在",
        });
      }

      // 检查订单状态是否为待审核
      if (existingOrder.status !== "pending") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只有待审核状态的订单可以审批",
        });
      }

      // 更新订单状态
      const newStatus = input.approved ? "approved" : "rejected";
      const updatedOrder = await prisma.purchaseOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: newStatus,
          approvedBy: ctx.user.id,
          approvedAt: new Date(),
          rejectReason: input.approved ? null : input.reason || "",
        },
      });

      return {
        code: 200,
        message: input.approved ? "采购订单审批通过" : "采购订单已拒绝",
        data: updatedOrder,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "审批采购订单失败",
        cause: error,
      });
    }
  });

// 创建收货记录
export const createPurchaseReceipt = singleResultProcedure
  .meta({ permission: ["purchase:receipt:create"], authRequired: true })
  .input(
    z.object({
      purchaseOrderId: z.number(),
      items: z.array(
        z.object({
          orderItemId: z.number(),
          receivedQuantity: z.number(),
          batchNo: z.string(),
          note: z.string().optional(),
        })
      ),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    const { purchaseOrderId, items, note } = input;

    // 事务处理：创建收货记录，更新采购订单状态，更新库存
    const result = await prisma.$transaction(async (tx) => {
      // 查询采购订单
      const purchaseOrder = await tx.purchaseOrder.findUnique({
        where: { id: purchaseOrderId },
        include: {
          items: true,
        },
      });

      if (!purchaseOrder) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "采购订单不存在",
        });
      }

      if (!purchaseOrder.warehouseId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "采购订单未指定仓库",
        });
      }

      // 创建收货记录
      const receipt = await tx.purchaseReceiptRecord.create({
        data: {
          purchaseOrderId,
          userId: ctx.user.id,
          note,
          items: {
            create: items,
          },
        },
        include: {
          items: true,
        },
      });

      // 更新库存
      for (const item of receipt.items) {
        const orderItem = purchaseOrder.items.find(
          (oi) => oi.id === item.orderItemId
        );
        if (!orderItem) continue;

        // 查找现有库存记录
        const existingStock = await tx.stock.findUnique({
          where: {
            materiel_id_warehouse_id_batch_no: {
              materiel_id: orderItem.materialId,
              warehouse_id: purchaseOrder.warehouseId,
              batch_no: item.batchNo,
            },
          },
        });

        if (existingStock) {
          // 更新现有库存记录
          await tx.stock.update({
            where: { id: existingStock.id },
            data: {
              quantity: {
                increment: item.receivedQuantity,
              },
            },
          });
        } else {
          // 创建新的库存记录
          await tx.stock.create({
            data: {
              materiel_id: orderItem.materialId,
              warehouse_id: purchaseOrder.warehouseId,
              batch_no: item.batchNo,
              quantity: item.receivedQuantity,
            },
          });
        }

        // 更新物料总库存
        await tx.materiel.update({
          where: { id: orderItem.materialId },
          data: {
            stock: {
              increment: item.receivedQuantity,
            },
          },
        });
      }

      // 检查是否所有物料都已完全收货
      const allItemsReceived = await tx.purchaseOrderItem
        .findMany({
          where: { purchaseOrderId },
          include: {
            receiptItems: true,
          },
        })
        .then((items) =>
          items.every((item) => {
            const totalReceived = item.receiptItems.reduce(
              (sum, ri) => sum + Number(ri.receivedQuantity),
              0
            );
            return totalReceived >= Number(item.quantity);
          })
        );

      // 更新采购订单状态
      await tx.purchaseOrder.update({
        where: { id: purchaseOrderId },
        data: {
          status: allItemsReceived ? "completed" : "partially_received",
        },
      });

      return receipt;
    });

    return {
      code: 1,
      message: "收货成功",
      data: result,
    };
  });

// 获取订单收货记录
export const getReceiptRecords = singleResultProcedure
  .meta({ permission: ["purchase:query"], authRequired: true })
  .input(
    z.object({
      orderId: z.number(),
    })
  )
  .query(async ({ input }) => {
    try {
      const records = await prisma.purchaseReceiptRecord.findMany({
        where: {
          purchaseOrderId: input.orderId,
        },
        include: {
          items: {
            include: {
              orderItem: {
                include: {
                  material: true,
                },
              },
            },
          },
          user: {
            select: {
              username: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return {
        code: 200,
        message: "success",
        data: records,
      };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取收货记录失败",
        cause: error,
      });
    }
  });

// 辅助函数：检查订单是否完全收货
async function checkOrderFullyReceived(orderId: number): Promise<boolean> {
  const order = await prisma.purchaseOrder.findUnique({
    where: {
      id: orderId,
    },
    include: {
      items: true,
      receiptRecords: {
        include: {
          items: true,
        },
      },
    },
  });

  if (!order) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "采购订单不存在",
    });
  }

  // 遍历每个订单项，检查收货数量是否等于订单数量
  for (const orderItem of order.items) {
    const receivedQuantity = order.receiptRecords.reduce((sum, record) => {
      const recordItem = record.items.find(
        (ri) => ri.orderItemId === orderItem.id
      );
      return sum + (recordItem ? Number(recordItem.receivedQuantity) : 0);
    }, 0);

    if (receivedQuantity < Number(orderItem.quantity)) {
      return false;
    }
  }

  return true;
}

// 合并所有采购相关路由
export const purchaseRouter = router({
  // 供应商相关
  querySupplier,
  addSupplier,
  updateSupplier,
  toggleSupplierLock,
  deleteSupplier,

  // 采购订单相关
  queryPurchaseOrders,
  getPurchaseOrder,
  createPurchaseOrder,
  updatePurchaseOrder,
  deletePurchaseOrder,
  submitPurchaseOrder,
  approvePurchaseOrder,

  // 收货相关
  createPurchaseReceipt,
  getReceiptRecords,
});
