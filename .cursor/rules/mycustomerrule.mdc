---
description: 
globs: 
alwaysApply: true
---

# Your rule content
- 使用中文与我交流
- 使用技术栈包括: Vue 3、Nuxt 3、JavaScript、TypeScript、Ant Design Vue、HTML 和 CSS
- 首先以伪代码的形式详细列出，确认后再编写代码
- 遵循 DRY 原则
- 注重代码的易读性，而非仅仅追求性能。
- 全面实现所请求的所有功能，避免任何待办事项、占位符或遗漏部分。
- 确保代码完整，最终代码经过彻底验证。
- 包括所有必要的导入，并确保关键组件的命名规范。
- 简明扼要，避免过多的文字。
- 如果您认为没有正确答案，您会直接说出来。
- 如果您不知道答案，您会坦诚表示，而不是猜测。
- 代码实现准则：尽可能使用早期返回，使代码更具可读性；
- 始终使用 Ant Design Vue 组件。
- 变量和函数/常量名称：使用具有描述性的变量和函数/常量名称。例如，事件函数应以 “handle” 前缀命名，如“handleClick”用于 onClick，“handleKeyDown”用于 onKeyDown。
- 无障碍功能：在元素上实现无障碍功能。例如，a 标签应有 tabindex="0"，aria-label，on:click 和 on:keydown 等属性。
- 常量代替函数：使用常量代替函数，例如，“const toggle = () =>”。如果可能，定义类型。
- 使用 Nuxt 3 的 useNuxtApp() 获取 Nuxt 3 的上下文。
- 使用 Nuxt 3 的 useRuntimeConfig() 获取 Nuxt 3 的配置。
- 使用 Nuxt 3 的 useRoute() 获取 Nuxt 3 的路由。
- 使用 Nuxt 3 的 useRouter() 获取 Nuxt 3 的路由器。
- 使用 Nuxt 3 的 useHead() 设置 Nuxt 3 的头部。
- 使用 tRPC 对数据进行操作。
- tRPC的客户端已经安装，使用 useApiTrpc()来调用接口
- 使用 zod 进行数据验证。
- 使用AntDesignVue中的表单时,表单验证规则通常先定义zod对象,再使用 [zodToAntRules.ts](mdc:utils/zodToAntRules.ts) 将zod转换成AntDesignVue表单规则并使用
- 使用 /issues 目录来管理代码 issue
    - /issues 目录下创建问题相关的文件 
    - 首层文件夹以 milestone 开头，如 m001-InitProject
    - 第二层每个 milestone 下的每个文件为一个 issue
    - {id}-f- 开头的文件 为 feature 文件
    - {id}-bug- 开头的文件 为 bug 文件
    - {id} 为在 /issues 目录下 issues 的全局唯一 id
    - 每个 issue 文件中包含
    - #Title 为 issue 的标题
    - #Introduction 为 issue 的描述
    - #Tasks 为 issue 的任务列表，每个任务以 - [ ] 开头
    - 如果有子任务，则以 - [ ] 开头，关联对应的任务文件
    - #Dependencies 为 issue 的依赖列表，每个依赖以 - [ ] 开头，关联对应的任务文件
    - 注意不要任意修改 /issues 目录下的文件内容，只能修改 - [ ] 任务状态,- [ ] 表示未完成,- [x] 表示已完成,- [-] 表示正在进行,- [*] 表示已跳过,- [!] 表示已放弃
    - 执行完任务后，请修改 issues 目录下对应的文件，填写任务内容。
    - 如果我没有通过issues就创建或修改了内容,请帮我在/issues下自动创建符合规则的内容
- 表格使用已经封装好的 [table.client.vue](mdc:components/manage/base/table.client.vue) 组件
- 需要选择物料时使用 [modelselector.client.vue](mdc:components/manage/materiel/modelselector.client.vue) 组件
- 需要选择用户时使用 [selector.client.vue](mdc:components/manage/user/selector.client.vue) 组件
- 需要选择角色时使用 [roleSelector.client.vue](mdc:components/manage/user/roleSelector.client.vue) 组件
- 所有页面都参照 [user.vue](mdc:pages/manage/mes/system/user.vue) 来设计
