import {
  router,
  mergeRouters,
  tableResultProcedure,
  singleResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { Prisma } from "@prisma/client";
import {
  productionOutboundSchema,
  createWasteProcessingRecordSchema,
  createDisinfectionRecordSchema,
} from "@/schemas/production";
import { Decimal } from "@prisma/client/runtime/library";
//查询生产计划
export const queryProductionPlan = tableResultProcedure
  .input(
    z.object({
      code: z.string().optional(),
      status: z.number().optional(),
      take: z.number().default(10),
      skip: z.number().default(0),
    })
  )
  .meta({ permission: ["production:query"], authRequired: true })
  .query(async ({ input }) => {
    const productionPlan = await prisma.productionPlan.findManyWithCount({
      take: input.take,
      skip: input.skip,
      include: {
        ProductionPlanItem: {
          include: {
            Materiel: true,
          },
        },
      },
    });
    return { code: 1, message: "success", data: productionPlan };
  });
//创建生产计划
export const createProductionPlan = singleResultProcedure
  .meta({ permission: ["production:create"], authRequired: true })
  .input(
    z.object({
      code: z.string(),
      name: z.string(),
      description: z.string().optional(),
      startTime: z.string().datetime(),
      endTime: z.string().datetime(),
      items: z.array(
        z.object({
          materiel_id: z.number(),
          quantity: z.number().min(1),
        })
      ),
    })
  )
  .mutation(async ({ input }) => {
    console.log(input);
    try {
      const data: Prisma.ProductionPlanCreateInput = {
        code: input.code,
        name: input.name,
        description: input.description,
        startTime: new Date(input.startTime),
        endTime: new Date(input.endTime),
        status: 0,
        ProductionPlanItem: {
          create: input.items.map((item) => ({
            materiel_id: item.materiel_id,
            quantity: new Prisma.Decimal(item.quantity),
          })),
        },
      };

      const plan = await prisma.productionPlan.create({ data });
      return { code: 1, message: "success", data: plan };
    } catch (error) {
      console.error("Create production plan error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "创建生产计划失败",
      });
    }
  });
//终止生产计划
export const terminatePlan = singleResultProcedure
  .meta({ permission: ["production:terminate"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查计划是否存在
      const plan = await prisma.productionPlan.findUnique({
        where: { id: input.id },
      });

      if (!plan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产计划不存在",
        });
      }

      // 检查计划状态
      if (plan.status === 2) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "生产计划已终止",
        });
      }

      // 更新计划状态为终止(2)
      const updatedPlan = await prisma.productionPlan.update({
        where: { id: input.id },
        data: { status: 2 },
      });

      return { code: 1, message: "success", data: updatedPlan };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error("Terminate production plan error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "终止生产计划失败",
      });
    }
  });
//发布生产计划
export const publishPlan = singleResultProcedure
  .meta({ permission: ["production:publish"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查计划是否存在
      const plan = await prisma.productionPlan.findUnique({
        where: { id: input.id },
      });

      if (!plan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产计划不存在",
        });
      }

      // 检查计划状态
      if (plan.status !== 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只有未发布的生产计划可以发布",
        });
      }

      // 更新计划状态为已发布(1)
      const updatedPlan = await prisma.productionPlan.update({
        where: { id: input.id },
        data: { status: 1 },
      });

      return { code: 1, message: "success", data: updatedPlan };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error("Publish production plan error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "发布生产计划失败",
      });
    }
  });
//查询生产工单
export const queryProductionTask = tableResultProcedure
  .meta({ permission: ["production:query"], authRequired: true })
  .input(
    z.object({
      quick: z.string().optional(),
      code: z.string().optional(),
      status: z
        .enum(["draft", "pending", "in_progress", "completed", "cancelled"])
        .optional(),
      includeUsers: z.boolean().optional(),
      take: z.number().default(10),
      skip: z.number().default(0),
    })
  )
  .query(async ({ input }) => {
    try {
      // 构建查询条件
      const where: Prisma.ProductionTaskWhereInput = {};
      if (input?.quick) {
        where.OR = [
          { code: { contains: input.quick } },
          { description: { contains: input.quick } },
        ];
      }
      if (input?.code) {
        where.code = {
          contains: input.code,
        };
      }
      switch (input?.status) {
        case "draft":
          where.status = 0;
          break;
        case "pending":
          where.status = 1;
          break;
        case "in_progress":
          where.status = 2;
          break;
        case "completed":
          where.status = 3;
          break;
        case "cancelled":
          where.status = 4;
          break;
      }
      // 查询数据
      const tasks = await prisma.productionTask.findManyWithCount({
        where,
        skip: input.skip,
        take: input.take,
        include: {
          Materiel: true,
          users: input?.includeUsers ? { include: { user: true } } : undefined,
        },
        orderBy: {
          createAt: "desc",
        },
      });

      return {
        code: 1,
        message: "success",
        data: tasks,
      };
    } catch (error) {
      console.error("Query production task error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "查询生产工单失败",
      });
    }
  });
//创建生产工单
export const createProductionTask = singleResultProcedure
  .meta({ permission: ["production:create"], authRequired: true })
  .input(
    z.object({
      code: z.string(),
      materiel_id: z.number(),
      quantity: z.number().min(1),
      description: z.string().optional(),
      startAt: z.string().date(),
      endAt: z.string().date(),
      status: z.number().default(0),
      finishCondition: z.enum(["quantity", "time"]).default("quantity"),
      users: z.array(
        z.object({
          user_id: z.number(),
        })
      ),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 验证物料是否存在
      const materiel = await prisma.materiel.findUnique({
        where: { id: input.materiel_id },
      });

      if (!materiel) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "所选产品不存在",
        });
      }

      // 验证结束时间必须大于开始时间
      const startTime = new Date(input.startAt);
      const endTime = new Date(input.endAt);

      if (endTime <= startTime) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "结束时间必须大于开始时间",
        });
      }

      // 创建生产工单
      const productionTask = await prisma.productionTask.create({
        data: {
          code: input.code,
          materiel_id: input.materiel_id,
          quantity: new Prisma.Decimal(input.quantity),
          description: input.description,
          startAt: startTime,
          endAt: endTime,
          status: input.status,
          finishCondition: input.finishCondition,
          users: {
            create: input.users.map((user) => ({
              user_id: user.user_id,
            })),
          },
        },
      });

      return { code: 1, message: "创建工单成功", data: productionTask };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Create production task error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "创建生产工单失败",
      });
    }
  });
//更新生产工单
export const updateProductionTask = singleResultProcedure
  .meta({ permission: ["production:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      materiel_id: z.number().optional(),
      quantity: z.number().min(1).optional(),
      description: z.string().optional(),
      startAt: z.string().datetime().optional(),
      endAt: z.string().datetime().optional(),
      status: z.number().optional(),
      finishCondition: z.enum(["quantity", "time"]).optional(),
      users: z
        .array(
          z.object({
            user_id: z.number(),
          })
        )
        .optional(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查工单是否存在
      const task = await prisma.productionTask.findUnique({
        where: { id: input.id },
        include: { users: true },
      });

      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产工单不存在",
        });
      }

      // 检查状态 - 只有草稿状态可以编辑全部字段
      if (task.status !== 0 && (input.materiel_id || input.quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "非草稿状态的工单不能修改产品和数量",
        });
      }

      // 准备更新数据
      const updateData: any = {};

      if (input.materiel_id) updateData.materiel_id = input.materiel_id;
      if (input.quantity) updateData.quantity = input.quantity;
      if (input.description !== undefined)
        updateData.description = input.description;
      if (input.startAt) updateData.startAt = new Date(input.startAt);
      if (input.endAt) updateData.endAt = new Date(input.endAt);
      if (input.status !== undefined) updateData.status = input.status;
      if (input.finishCondition)
        updateData.finishCondition = input.finishCondition;

      // 更新操作员关联
      if (input.users) {
        // 先删除现有关联
        await prisma.productionTaskUsers.deleteMany({
          where: { task_id: input.id },
        });

        // 添加新关联
        updateData.users = {
          create: input.users,
        };
      }

      // 执行更新
      const updatedTask = await prisma.productionTask.update({
        where: { id: input.id },
        data: updateData,
        include: {
          Materiel: true,
          users: {
            include: {
              user: true,
            },
          },
        },
      });

      return { code: 1, message: "更新工单成功", data: updatedTask };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Update production task error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新生产工单失败",
      });
    }
  });
//删除生产工单
export const deleteProductionTask = singleResultProcedure
  .meta({ permission: ["production:delete"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查工单是否存在
      const task = await prisma.productionTask.findUnique({
        where: { id: input.id },
      });

      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产工单不存在",
        });
      }

      // 检查状态 - 只有草稿状态可以删除
      if (task.status !== 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只有草稿状态的工单可以删除",
        });
      }

      // 删除关联的操作员
      await prisma.productionTaskUsers.deleteMany({
        where: { task_id: input.id },
      });

      // 删除工单
      await prisma.productionTask.delete({
        where: { id: input.id },
      });

      return { code: 1, message: "删除工单成功" };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Delete production task error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除生产工单失败",
      });
    }
  });
//更新生产工单状态
export const updateProductionTaskStatus = singleResultProcedure
  .meta({ permission: ["production:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      status: z.number().min(0).max(4),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 检查工单是否存在
      const task = await prisma.productionTask.findUnique({
        where: { id: input.id },
      });

      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产工单不存在",
        });
      }

      // 状态转换逻辑
      const currentStatus = task.status;
      const newStatus = input.status;

      // 状态转换验证
      if (currentStatus === newStatus) {
        return { code: 1, message: "状态未变更", data: task };
      }

      let updateData: Prisma.ProductionTaskUpdateInput = {
        status: newStatus,
      };

      // 特殊状态处理
      if (newStatus === 2 && !task.actualStartAt) {
        // 开始生产
        updateData.actualStartAt = new Date();
      } else if (newStatus === 3 && !task.actualEndAt) {
        // 完成生产
        updateData.actualEndAt = new Date();
      }

      // 执行更新
      const updatedTask = await prisma.productionTask.update({
        where: { id: input.id },
        data: updateData,
      });

      return { code: 1, message: "更新工单状态成功", data: updatedTask };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Update production task status error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新生产工单状态失败",
      });
    }
  });
//更新生产工单完成数量,并记录报工
export const updateProductionTaskQuantity = singleResultProcedure
  .meta({ permission: ["production:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      completed_quantity: z.number().min(0),
      quantity: z.number().min(0),
      unqualified_quantity: z.number().min(0).optional(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ ctx, input }) => {
    try {
      // 检查工单是否存在
      const task = await prisma.productionTask.findUnique({
        where: { id: input.id },
      });

      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产工单不存在",
        });
      }

      // 检查状态 - 只有生产中状态可以更新完成数量
      if (task.status !== 2) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只有生产中的工单可以更新完成数量",
        });
      }

      // 计算合格品数量 (completed_quantity - unqualified_quantity)
      const qualifiedQuantity =
        input.unqualified_quantity !== undefined
          ? input.completed_quantity - input.unqualified_quantity
          : input.completed_quantity;

      // 准备更新数据
      const updateData: any = {
        completed_quantity: new Prisma.Decimal(input.completed_quantity),
        qualified_quantity: new Prisma.Decimal(
          qualifiedQuantity >= 0 ? qualifiedQuantity : 0
        ),
      };

      // 如果有不合格品数量，则更新不合格品数量
      if (input.unqualified_quantity !== undefined) {
        updateData.unqualified_quantity = new Prisma.Decimal(
          input.unqualified_quantity
        );
      }

      // 执行更新
      const updatedTask = await prisma.productionTask.update({
        where: { id: input.id },
        data: updateData,
      });
      // 记录报工
      await prisma.productionReport.create({
        data: {
          production_task_id: input.id,
          user_id: ctx.user.id,
          // quantity: qualifiedQuantity >= 0 ? qualifiedQuantity : 0,
          quantity: input.quantity >= 0 ? input.quantity : 0,
          unqualified_quantity: new Prisma.Decimal(
            input.unqualified_quantity || 0
          ),
          note: input.note,
        },
      });

      // 检查是否达到完成条件
      // if (
      //   task.finishCondition === "quantity" &&
      //   input.completed_quantity >= Number(task.quantity)
      // ) {
      //   // 自动完成工单
      //   await prisma.productionTask.update({
      //     where: { id: input.id },
      //     data: {
      //       status: 3,
      //       actualEndAt: new Date(),
      //     },
      //   });

      //   return {
      //     code: 1,
      //     message: "更新完成数量成功，工单已自动完成",
      //     data: {
      //       ...updatedTask,
      //       status: 3,
      //       actualEndAt: new Date(),
      //     },
      //   };
      // }

      return { code: 1, message: "更新完成数量成功", data: updatedTask };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Update production task quantity error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新生产工单完成数量失败",
      });
    }
  });

// 生产领料
export const productionOutbound = singleResultProcedure
  .meta({ permission: ["production:outbound"], authRequired: true })
  .input(productionOutboundSchema)
  .mutation(async ({ input }) => {
    console.log(input);
    try {
      // 检查工单是否存在
      const task = await prisma.productionTask.findUnique({
        where: { id: input.task_id },
      });
      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产工单不存在",
        });
      }
      // 检查物料库存是否充足
      // 检查每个物料的库存是否充足
      const stockChecks = await Promise.all(
        input.bom_items.map(async (item) => {
          const stock = await prisma.stock.findFirst({
            where: {
              materiel_id: item.id,
              batch_no: item.batch_no,
              quantity: {
                gte: item.quantity,
              },
            },
          });
          return {
            item,
            stock,
            sufficient: !!stock,
          };
        })
      );

      // 检查是否所有物料都库存充足
      const insufficientItems = stockChecks.filter(
        (check) => !check.sufficient
      );
      console.log(insufficientItems);
      if (insufficientItems.length > 0) {
        return {
          code: 0,
          message: `物料库存不足,请检查`,
        };
      }

      // 扣减库存,并记录出库
      // 开启事务处理库存扣减和出库记录
      await prisma.$transaction(async (tx) => {
        // 扣减库存
        for (const item of input.bom_items) {
          // console.log(item);
          await tx.stock.update({
            where: {
              materiel_id_warehouse_id_batch_no: {
                materiel_id: item.id,
                warehouse_id: item.warehouse_id,
                batch_no: item.batch_no,
              },
            },
            data: {
              quantity: {
                decrement: item.quantity,
              },
            },
          });

          // 创建出库记录
          await tx.productionOutRecord.create({
            data: {
              productionTaskId: input.task_id,
              materiel_id: item.id,
              quantity: item.quantity,
              batch_no: item.batch_no,
            },
          });
        }
        // 更新工单状态
        await tx.productionTask.update({
          where: { id: input.task_id },
          data: { status: 2 },
        });
      });
      return { code: 1, message: "生产领料成功" };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Production outbound error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "生产领料失败",
      });
    }
  });

// 查询废料处理记录
export const queryWasteProcessingRecord = tableResultProcedure
  .meta({ permission: ["waste:query"], authRequired: true })
  .input(
    z.object({
      take: z.number().default(10),
      skip: z.number().default(0),
    })
  )
  .query(async ({ input }) => {
    const { take, skip } = input;
    const where: Prisma.WasteProcessingRecordWhereInput = {};
    const wasteProcessingRecord =
      await prisma.wasteProcessingRecord.findManyWithCount({
        where,
        take,
        skip,
        include: {
          materiel: true,
        },
      });
    return {
      code: 1,
      message: "查询废料处理记录成功",
      data: wasteProcessingRecord,
    };
  });
//新增废品处理记录
export const createWasteProcessingRecord = singleResultProcedure
  .meta({ permission: ["waste:create"], authRequired: true })
  .input(createWasteProcessingRecordSchema)
  .mutation(async ({ input }) => {
    try {
      // 检查物料是否存在
      const materiel = await prisma.materiel.findUnique({
        where: { id: input.materiel_id },
      });
      if (!materiel) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "物料不存在",
        });
      }
      // 检查库存是否充足
      const stock = await prisma.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id: input.materiel_id,
            warehouse_id: input.warehouse_id, // 使用前端传入的仓库ID
            batch_no: input.batch_no,
          },
        },
      });
      if (!stock) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "找不到指定批次的库存记录",
        });
      }

      // 检查库存是否足够
      if (stock.quantity.lessThan(input.quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存不足",
        });
      }

      // 扣减库存
      // 计算扣减后的数量
      const remainingQuantity = stock.quantity.minus(input.quantity);

      // 如果扣减后数量为0，则删除该库存记录
      if (remainingQuantity.equals(0)) {
        await prisma.stock.delete({
          where: {
            id: stock.id,
          },
        });
      } else {
        // 否则更新库存数量
        await prisma.stock.update({
          where: {
            id: stock.id, // 使用找到的库存记录ID进行更新，更可靠
          },
          data: {
            quantity: remainingQuantity,
          },
        });
      }
      //更新物料表数量
      await prisma.materiel.update({
        where: { id: input.materiel_id },
        data: {
          stock: remainingQuantity,
        },
      });
      // 创建废品处理记录
      await prisma.wasteProcessingRecord.create({
        data: {
          materiel_id: input.materiel_id,
          quantity: input.quantity,
          batch_no: input.batch_no,
          note: input.remark,
        },
      });
      return { code: 1, message: "新增废品处理记录成功" };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Create waste processing record error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "新增废品处理记录失败",
      });
    }
  });

// 查询消毒处理记录
export const queryDisinfectionRecord = tableResultProcedure
  .meta({ permission: ["production:query"], authRequired: true })
  .input(
    z.object({
      take: z.number().default(10),
      skip: z.number().default(0),
    })
  )
  .query(async ({ input }) => {
    const { take, skip } = input;
    const where: Prisma.DisinfectionRecordWhereInput = {};
    const disinfectionRecord =
      await prisma.disinfectionRecord.findManyWithCount({
        where,
        take,
        skip,
        include: {
          materiel: true,
        },
      });
    return {
      code: 1,
      message: "查询消毒处理记录成功",
      data: disinfectionRecord,
    };
  });

//新增消毒处理记录
export const createDisinfectionRecord = singleResultProcedure
  .meta({ permission: ["production:create"], authRequired: true })
  .input(createDisinfectionRecordSchema)
  .mutation(async ({ input }) => {
    try {
      // 检查物料是否存在
      const materiel = await prisma.materiel.findUnique({
        where: { id: input.materiel_id },
      });
      if (!materiel) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "物料不存在",
        });
      }
      // 检查库存是否充足
      const stock = await prisma.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id: input.materiel_id,
            warehouse_id: input.warehouse_id, // 使用前端传入的仓库ID
            batch_no: input.batch_no,
          },
        },
      });
      if (!stock) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "找不到指定批次的库存记录",
        });
      }

      // 检查库存是否足够
      if (stock.quantity.lessThan(input.quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存不足",
        });
      }

      // 扣减库存
      // 计算扣减后的数量
      const remainingQuantity = stock.quantity.minus(input.quantity);

      // 如果扣减后数量为0，则删除该库存记录
      if (remainingQuantity.equals(0)) {
        await prisma.stock.delete({
          where: {
            id: stock.id,
          },
        });
      } else {
        // 否则更新库存数量
        await prisma.stock.update({
          where: {
            id: stock.id, // 使用找到的库存记录ID进行更新，更可靠
          },
          data: {
            quantity: remainingQuantity,
          },
        });
      }
      // 创建消毒处理记录
      await prisma.disinfectionRecord.create({
        data: {
          materiel_id: input.materiel_id,
          quantity: input.quantity,
          batch_no: input.batch_no,
          note: input.remark,
        },
      });
      return { code: 1, message: "新增消毒处理记录成功" };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Create disinfection record error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "新增消毒处理记录失败",
      });
    }
  });

// 查询生产领料记录
export const queryProductionOutRecords = tableResultProcedure
  .meta({ permission: ["production:query"], authRequired: true })
  .input(
    z.object({
      taskId: z.number().optional(),
      materielId: z.number().optional(),
      take: z.number().default(10),
      skip: z.number().default(0),
    })
  )
  .query(async ({ input }) => {
    try {
      const { taskId, materielId, take, skip } = input;
      const where: Prisma.ProductionOutRecordWhereInput = {};

      if (taskId) {
        where.productionTaskId = taskId;
      }

      if (materielId) {
        where.materiel_id = materielId;
      }

      const records = await prisma.productionOutRecord.findManyWithCount({
        where,
        take,
        skip,
        include: {
          materiel: true,
          productionTask: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return {
        code: 1,
        message: "查询生产领料记录成功",
        data: records,
      };
    } catch (error) {
      console.error("Query production out records error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "查询生产领料记录失败",
      });
    }
  });

// 获取工单BOM清单和已领料汇总
export const getTaskBomWithOutboundSummary = singleResultProcedure
  .meta({ permission: ["production:query"], authRequired: true })
  .input(
    z.object({
      taskId: z.number(),
    })
  )
  .query(async ({ input }) => {
    try {
      const { taskId } = input;

      // 查询工单信息
      const task = await prisma.productionTask.findUnique({
        where: { id: taskId },
        include: {
          Materiel: true,
        },
      });

      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产工单不存在",
        });
      }

      // 查询BOM清单
      const bomList = await prisma.materielBom.findMany({
        where: {
          parentId: task.materiel_id,
        },
        include: {
          child: true,
        },
      });

      // 查询已领料汇总 - 按物料分组统计
      const outboundSummary = await prisma.productionOutRecord.groupBy({
        by: ["materiel_id"],
        where: {
          productionTaskId: taskId,
        },
        _sum: {
          quantity: true,
        },
      });

      // 将已领料数量映射到BOM清单
      const bomWithOutbound = bomList.map((bomItem) => {
        const outboundItem = outboundSummary.find(
          (summary) => summary.materiel_id === bomItem.childId
        );
        const outboundQuantity = outboundItem?._sum.quantity || new Decimal(0);
        const planQuantity = new Decimal(bomItem.quantity).mul(task.quantity);
        const remainingQuantity = planQuantity.sub(outboundQuantity);

        return {
          uid: bomItem.child.id,
          id: bomItem.child.id,
          code: bomItem.child.code,
          name: bomItem.child.name,
          model: bomItem.child.model,
          unit: bomItem.child.unit,
          specification: bomItem.child.specification,
          planQuantity: planQuantity.toNumber(), // 计划所需数量 = BOM单位用量 * 工单计划数量
          outboundQuantity: outboundQuantity.toNumber(), // 已领数量
          remainingQuantity: remainingQuantity.toNumber(), // 待领数量
          quantity: remainingQuantity.toNumber(), // 默认显示待领数量
          note: bomItem.remark,
          warehouse_id: bomItem.child.warehouse_id,
        };
      });

      return {
        code: 1,
        message: "获取工单BOM清单和已领料汇总成功",
        data: {
          task,
          bomWithOutbound,
        },
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Get task BOM with outbound summary error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取工单BOM清单和已领料汇总失败",
      });
    }
  });

// 查询生产报工记录
export const getProductionReports = singleResultProcedure
  .meta({ permission: ["production:query"], authRequired: true })
  .input(
    z.object({
      taskId: z.number(),
    })
  )
  .query(async ({ input }) => {
    try {
      const { taskId } = input;

      // 查询指定工单的报工记录
      const reports = await prisma.productionReport.findMany({
        where: {
          production_task_id: taskId,
        },
        include: {
          user: true,
        },
        orderBy: {
          createAt: "desc",
        },
      });

      return {
        code: 1,
        message: "查询生产报工记录成功",
        data: reports,
      };
    } catch (error) {
      console.error("Query production reports error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "查询生产报工记录失败",
      });
    }
  });

// 查询生产入库记录
export const getProductionInRecords = singleResultProcedure
  .meta({ permission: ["production:query"], authRequired: true })
  .input(
    z.object({
      taskId: z.number(),
    })
  )
  .query(async ({ input }) => {
    try {
      const { taskId } = input;

      // 查询指定工单的入库记录
      const records = await prisma.productionInRecord.findMany({
        where: {
          productionTaskId: taskId,
        },
        include: {
          materiel: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return {
        code: 1,
        message: "查询生产入库记录成功",
        data: records,
      };
    } catch (error) {
      console.error("Query production in records error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "查询生产入库记录失败",
      });
    }
  });

// 创建生产入库记录
export const createProductionInRecord = singleResultProcedure
  .meta({ permission: ["production:inbound"], authRequired: true })
  .input(
    z.object({
      productionTaskId: z.number(),
      materiel_id: z.number(),
      quantity: z.number(),
      batch_no: z.string(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      const { productionTaskId, materiel_id, quantity, batch_no, note } = input;

      // 检查工单是否存在
      const task = await prisma.productionTask.findUnique({
        where: { id: productionTaskId },
      });

      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "生产工单不存在",
        });
      }

      // 检查物料是否存在
      const materiel = await prisma.materiel.findUnique({
        where: { id: materiel_id },
      });

      if (!materiel) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "物料不存在",
        });
      }

      // 创建生产入库记录
      const record = await prisma.productionInRecord.create({
        data: {
          productionTaskId,
          materiel_id,
          quantity: new Prisma.Decimal(quantity),
          batch_no,
          note,
        },
      });

      return {
        code: 1,
        message: "创建生产入库记录成功",
        data: record,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Create production in record error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "创建生产入库记录失败",
      });
    }
  });

export default router({
  queryProductionPlan,
  createProductionPlan,
  terminatePlan,
  publishPlan,
  queryProductionTask,
  createProductionTask,
  updateProductionTask,
  deleteProductionTask,
  updateProductionTaskStatus,
  productionOutbound,
  queryWasteProcessingRecord,
  createWasteProcessingRecord,
  queryDisinfectionRecord,
  createDisinfectionRecord,
  queryProductionOutRecords,
  getTaskBomWithOutboundSummary,
  getProductionReports,
  getProductionInRecords,
  createProductionInRecord,
});
