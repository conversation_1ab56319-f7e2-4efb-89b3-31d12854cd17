-- CreateTable
CREATE TABLE `StockCheck` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `checkNo` VARCHAR(255) NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `warehouse_id` INTEGER NOT NULL,
    `status` VARCHAR(50) NOT NULL DEFAULT 'draft',
    `checkType` VARCHAR(50) NOT NULL DEFAULT 'full',
    `plannedDate` DATETIME(3) NOT NULL,
    `actualDate` DATETIME(3) NULL,
    `checkUser_id` INTEGER NOT NULL,
    `approveUser_id` INTEGER NULL,
    `approvedAt` DATETIME(3) NULL,
    `note` TEXT NULL,
    `totalItems` INTEGER NOT NULL DEFAULT 0,
    `checkedItems` INTEGER NOT NULL DEFAULT 0,
    `differenceItems` INTEGER NOT NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `StockCheck_checkNo_key`(`checkNo`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `StockCheckItem` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `stockCheck_id` INTEGER NOT NULL,
    `materiel_id` INTEGER NOT NULL,
    `batch_no` VARCHAR(255) NOT NULL,
    `location` VARCHAR(255) NULL,
    `systemQuantity` DECIMAL(20, 4) NOT NULL,
    `actualQuantity` DECIMAL(20, 4) NULL,
    `differenceQuantity` DECIMAL(20, 4) NULL,
    `status` VARCHAR(50) NOT NULL DEFAULT 'pending',
    `note` TEXT NULL,
    `checkUser_id` INTEGER NULL,
    `checkedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `StockCheck` ADD CONSTRAINT `StockCheck_warehouse_id_fkey` FOREIGN KEY (`warehouse_id`) REFERENCES `Warehouse`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StockCheck` ADD CONSTRAINT `StockCheck_checkUser_id_fkey` FOREIGN KEY (`checkUser_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StockCheck` ADD CONSTRAINT `StockCheck_approveUser_id_fkey` FOREIGN KEY (`approveUser_id`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StockCheckItem` ADD CONSTRAINT `StockCheckItem_stockCheck_id_fkey` FOREIGN KEY (`stockCheck_id`) REFERENCES `StockCheck`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StockCheckItem` ADD CONSTRAINT `StockCheckItem_materiel_id_fkey` FOREIGN KEY (`materiel_id`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StockCheckItem` ADD CONSTRAINT `StockCheckItem_checkUser_id_fkey` FOREIGN KEY (`checkUser_id`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
